<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        code,
        plan_id,
        district_area_id,
        (select name from sm_circuit_district_area where id = district_area_id) district_area_name,
        is_normal_plan,
        is_need_feedback,
        move_type,
        devices,
        special_devices,
        name,
        receive_user_id,
        collaborate_user_id,
        present_distance,
        status,
        remark,
        reject_reason,
        audit_user_id,
        audit_time,
        <!--@formatter:off-->
        <!--presentState-->
       (select
    (select count(1) from sm_circuit_task_report where task_code = code and tenant_id = sm_circuit_task.tenant_id and is_fallback = true)
    || '/' ||
    (select count(1) from sm_circuit_task_report where task_code = code and tenant_id = sm_circuit_task.tenant_id)
        )  as presentState,
        <!--fallbackState-->
        (select
    (select count(1) from sm_circuit_task_report where task_code = code and is_fallback = true and type = '关键点')
    || '/' ||
    (select count(1) from sm_circuit_task_report where task_code = code and type = '关键点')
        )  as fallbackState,
        <!--keyPointCount-->
        (select count(1) from sm_circuit_task_report where task_code = code and type = '关键点' and tenant_id = #{tenantId}) as keyPointCount,
        <!--deviceCount-->
        (select count(1) from sm_circuit_task_report where task_code = code and type != '关键点' and tenant_id = #{tenantId}) as deviceCount,
        <!--@formatter:on-->
        creator,
        create_time,
        begin_time,
        end_time,
        tenant_id,
        sm_circuit_plan_get_plan_circle_by_id(plan_id) plan_circle<!--@sql from sm_circuit_task -->
    </sql>

    <sql id="Base_Column_List_With_Alias">
        <!--@sql select -->
        t.id,
        t.code,
        t.plan_id,
        t.district_area_id,
        (select name from sm_circuit_district_area where id = t.district_area_id) district_area_name,
        t.is_normal_plan,
        t.is_need_feedback,
        t.move_type,
        t.devices,
        t.special_devices,
        t.name,
        t.receive_user_id,
        t.collaborate_user_id,
        t.present_distance,
        t.status,
        t.remark,
        t.reject_reason,
        t.audit_user_id,
        t.audit_time,
        <!--@formatter:off-->
        <!--presentState-->
       (select
    (select count(1) from sm_circuit_task_report where task_code = t.code and tenant_id = t.tenant_id and is_fallback = true)
    || '/' ||
    (select count(1) from sm_circuit_task_report where task_code = t.code and tenant_id = t.tenant_id)
        )  as presentState,
        <!--fallbackState-->
        (select
    (select count(1) from sm_circuit_task_report where task_code = t.code and is_fallback = true and type = '关键点')
    || '/' ||
    (select count(1) from sm_circuit_task_report where task_code = t.code and type = '关键点')
        )  as fallbackState,
        <!--keyPointCount-->
        (select count(1) from sm_circuit_task_report where task_code = t.code and type = '关键点' and tenant_id = #{tenantId}) as keyPointCount,
        <!--deviceCount-->
        (select count(1) from sm_circuit_task_report where task_code = t.code and type != '关键点' and tenant_id = #{tenantId}) as deviceCount,
        <!--@formatter:on-->
        t.creator,
        t.create_time,
        t.begin_time,
        t.end_time,
        t.tenant_id,
        sm_circuit_plan_get_plan_circle_by_id(t.plan_id) plan_circle<!--@sql from sm_circuit_task t -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskResponse">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="plan_id" property="planId"/>
        <result column="district_area_id" property="districtAreaId"/>
        <result column="district_area_name" property="districtAreaName"/>
        <result column="is_normal_plan" property="isNormalPlan"/>
        <result column="is_need_feedback" property="isNeedFeedback"/>
        <result column="move_type" property="moveType"/>
        <result column="devices" property="devices"/>
        <result column="special_devices" property="specialDevices"/>
        <result column="name" property="name"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="collaborate_user_id" property="collaborateUserId"/>
        <result column="present_distance" property="presentDistance"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="presentState" property="presentState"/>
        <result column="fallbackState" property="fallbackState"/>
        <result column="keyPointCount" property="keyPointCount"/>
        <result column="deviceCount" property="deviceCount"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="plan_circle" property="planCircle"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="audit_time" property="auditTime"/>
        <result column="reject_reason" property="rejectReason"/>
        <!-- 表单完成率相关字段 -->
        <result column="form_completion_rate" property="formCompletionRate"/>
        <result column="total_form_records" property="totalFormRecords"/>
        <result column="completed_form_records" property="completedFormRecords"/>
    </resultMap>

    <insert id="saveAll">
        INSERT INTO sm_circuit_task(id,
                                    code,
                                    plan_id,
                                    district_area_id,
                                    is_normal_plan,
                                    is_need_feedback,
                                    move_type,
                                    devices,
                                    special_devices,
                                    name,
                                    receive_user_id,
                                    collaborate_user_id,
                                    present_distance,
                                    status,
                                    remark,
                                    creator,
                                    create_time,
                                    begin_time,
                                    end_time,
                                    tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             generate_number_reset_different_day_with_date_prefix('sm_circuit_task' || #{element.tenantId}, 'fm0000',
                                                                  9999),
             #{element.planId},
             #{element.districtAreaId},
             #{element.isNormalPlan},
             #{element.isNeedFeedback},
             #{element.moveType},
             #{element.devices},
             #{element.specialDevices},
             #{element.name},
             #{element.receiveUserId},
             #{element.collaborateUserId},
             #{element.presentDistance},
             #{element.status},
             #{element.remark},
             #{element.creator},
             #{element.createTime},
             #{element.beginTime},
             #{element.endTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update sm_circuit_task
        <set>
            code                = valueTable.code,
            plan_id             = valueTable.planId,
            district_area_id    = valueTable.districtAreaId,
            is_normal_plan      = valueTable.isNormalPlan,
            is_need_feedback    = valueTable.isNeedFeedback,
            move_type           = valueTable.moveType,
            devices             = valueTable.devices,
            special_devices     = valueTable.specialDevices,
            name                = valueTable.name,
            receive_user_id     = valueTable.receiveUserId,
            collaborate_user_id = valueTable.collaborateUserId,
            present_distance    = valueTable.presentDistance,
            remark              = valueTable.remark,
            creator             = valueTable.creator,
            create_time         = valueTable.createTime,
            begin_time          = valueTable.beginTime,
            end_time            = valueTable.endTime,
            tenant_id           = valueTable.tenantId
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.code},
             #{element.planId},
             #{element.districtAreaId},
             #{element.isNormalPlan},
             #{element.isNeedFeedback},
             #{element.moveType},
             #{element.devices},
             #{element.specialDevices},
             #{element.name},
             #{element.receiveUserId},
             #{element.collaborateUserId},
             #{element.presentDistance},
             #{element.remark},
             #{element.creator},
             #{element.createTime},
             #{element.beginTime},
             #{element.endTime},
             #{element.tenantId})
        </foreach>
        ) as valueTable(id, code, planId, districtAreaId, isNormalPlan, isNeedFeedback, moveType, devices,
                        specialDevices, name, receiveUserId, collaborateUserId, presentDistance, remark, creator,
                        createTime, beginTime, endTime, tenantId)
        where sm_circuit_task.id = valueTable.id
    </update>

    <update id="update">
        update sm_circuit_task
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="planId != null">
                plan_id = #{planId},
            </if>
            <if test="districtAreaId != null">
                district_area_id = #{districtAreaId},
            </if>
            <if test="isNormalPlan != null">
                is_normal_plan = #{isNormalPlan},
            </if>
            <if test="isNeedFeedback != null">
                is_need_feedback = #{isNeedFeedback},
            </if>
            <if test="moveType != null">
                move_type = #{moveType},
            </if>
            <if test="devices != null">
                devices = #{devices},
            </if>
            <if test="specialDevices != null">
                special_devices = #{specialDevices},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="receiveUserId != null">
                receive_user_id = #{receiveUserId},
            </if>
            <if test="collaborateUserId != null">
                collaborate_user_id = #{collaborateUserId},
            </if>
            <if test="presentDistance != null">
                present_distance = #{presentDistance},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_task
        where id = #{id}
    </select>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_With_Alias"/>,
        -- 表单完成率相关字段
        COALESCE(form_stats.total_form_records, 0) as total_form_records,
        COALESCE(form_stats.completed_form_records, 0) as completed_form_records,
        CASE
            WHEN COALESCE(form_stats.total_form_records, 0) = 0 THEN 0
            ELSE ROUND(COALESCE(form_stats.completed_form_records, 0) * 100.0 / form_stats.total_form_records)
        END as form_completion_rate
        from sm_circuit_task t
        LEFT JOIN (
            SELECT
                task_code,
                COUNT(*) as total_form_records,
                COUNT(CASE WHEN result != 'NOT_CHECKED' THEN 1 END) as completed_form_records
            FROM sm_circuit_task_form_record
            GROUP BY task_code
        ) form_stats ON t.code = form_stats.task_code
        <where>
            <if test="keyword != null and keyword != ''">
                and (t.code like '%' || #{keyword} || '%' or t.name like '%' || #{keyword} || '%' or
                     t.remark like '%' || #{keyword} || '%')
            </if>
            <if test="isNormalPlan != null">
                and t.is_normal_plan = #{isNormalPlan}
            </if>
            <if test="isReceived != null">
                and (t.status in
                <foreach collection="atAssignedStatus" open="(" close=")" separator="," item="element">
                    #{element}
                </foreach>) = #{isReceived}
            </if>
            <if test="isComplete != null">
                and (t.status = #{actualCompleteStatus}) = #{isComplete}
            </if>
            <if test="receiveUserId != null and receiveUserId != ''">
                and (t.receive_user_id = #{receiveUserId} or t.collaborate_user_id = #{receiveUserId})
            </if>
            <if test="collaborateUserId != null and collaborateUserId != ''">
                and t.collaborate_user_id like '%' || #{collaborateUserId} || '%'
            </if>
            <if test="creator != null and creator != ''">
                and t.creator = #{creator}
            </if>
            <if test="beginTimeFrom != null">
                and t.begin_time >= #{beginTimeFrom}
            </if>
            <if test="beginTimeTo != null">
                and t.begin_time &lt;= #{beginTimeTo}
            </if>
            <if test="fromTime != null">
                and t.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and t.create_time &lt;= #{toTime}
            </if>
            and t.tenant_id = #{tenantId}
        </where>
        order by t.create_time desc
    </select>

    <update id="assign">
        update sm_circuit_task
        set receive_user_id     = #{receiveUserId},
            collaborate_user_id = #{collaborateUserId},
            status              = #{verifyStatus}
        where id = #{taskId}
    </update>

    <update id="assignBatch">
        update sm_circuit_task
        set receive_user_id     = #{receiveUserId},
            collaborate_user_id = #{collaborateUserId},
            status              = #{verifyStatus}
        where id in
        <foreach collection="taskIdList" item="element" open="(" close=")" separator=",">
            #{element}
        </foreach>
    </update>

    <insert id="relateWorkOrder">
        insert into sm_circuit_plan_work_order(task_code, order_code, point_id, tenant_id)
        values (#{taskCode}, #{workOrderSerialNo}, #{pointId}, #{tenantId})
    </insert>

    <sql id="Pitfall_Workorder_Column_List">
        <!--@sql select -->
        task.code,
        workorder.serial_no                                                work_order_code,
        workorder.id                                                       work_order_id,
        workorder.type                                                     type_or_work_order,
        workorder.title                                                    "content",
        workorder.organizer_id                                             creator_of_work_order,
        workorder.create_time                                              create_time_of_work_order,
        workorder.level,
        workorder.status,
        joint.point_id,
        (select name from sm_circuit_district_point where id = point_id)   point_name,
        (select count(1) > 0
         from sm_circuit_plan_work_order
         where task_code = task.code
           and tenant_id = task.tenant_id)                                 is_pitfall,
        process_level,
        source,
        upload_user_id,
        ifnull(upload_phone,
               (select phone from tb_user u where u.id = organizer_id)) as upload_phone,
        upload_no,
        upload_address,
        cc_user_id,
        user_resolve_multi_id(cc_user_id)                               as cc_user_name,
        video_url,
        audio_url,
        img_url,
        other_file_url,
        coordinate,
        coordinate_name<!--@sql from sm_circuit_plan_work_order joint, sm_circuit_task task, work_order workorder -->
    </sql>
    <resultMap id="SMCircuitTaskWorkOrderPitfallResponseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskWorkOrderPitfallResponse">
        <result column="code" property="code"/>
        <result column="work_order_code" property="workOrderCode"/>
        <result column="work_order_id" property="workOrderId"/>
        <result column="type_or_work_order" property="type"/>
        <result column="content" property="content"/>
        <result column="creator_of_work_order" property="creator"/>
        <result column="create_time_of_work_order" property="createTime"/>
        <result column="level" property="level"/>
        <result column="status" property="status"/>
        <result column="point_id" property="pointId"/>
        <result column="point_name" property="pointName"/>
        <result column="is_pitfall" property="isPitfall"/>
        <result column="process_level" property="processLevel"/>
        <result column="source" property="source"/>
        <result column="upload_user_id" property="uploadUserId"/>
        <result column="upload_phone" property="uploadPhone"/>
        <result column="upload_no" property="uploadNo"/>
        <result column="upload_address" property="uploadAddress"/>
        <result column="cc_user_id" property="ccUserId"/>
        <result column="cc_user_name" property="ccUserName"/>
        <result column="video_url" property="videoUrl"/>
        <result column="audio_url" property="audioUrl"/>
        <result column="img_url" property="imgUrl"/>
        <result column="other_file_url" property="otherFileUrl"/>
        <result column="coordinate" property="coordinate"/>
        <result column="coordinate_name" property="coordinateName"/>
    </resultMap>

    <select id="findPitfallWorkorderInfoByPointId" resultMap="SMCircuitTaskWorkOrderPitfallResponseResultMap">
        select
        <include refid="Pitfall_Workorder_Column_List"/>
        from sm_circuit_plan_work_order joint
                 left join sm_circuit_task task
                           on task.code = joint.task_code
                 left join work_order workorder
                           on workorder.serial_no = joint.order_code
        where joint.task_code = #{taskCode}
          and joint.point_id = #{pointId}
          and joint.tenant_id = #{tenantId}
          and task.create_time = (select max(create_time)
                                  from sm_circuit_plan_work_order joint
                                           left join sm_circuit_task task
                                                     on task.code = joint.task_code
                                  where joint.task_code = #{taskCode}
                                    and joint.point_id = #{pointId}
                                    and joint.tenant_id = #{tenantId})
    </select>

    <select id="findPitfallWorkorderInfo"
            resultMap="SMCircuitTaskWorkOrderPitfallResponseResultMap">
        select
        <include refid="Pitfall_Workorder_Column_List"/>
        from sm_circuit_plan_work_order joint
                 left join sm_circuit_task task
                           on task.code = joint.task_code
                 left join work_order workorder
                           on workorder.serial_no = joint.order_code
        <where>
            <if test="statusStage != null">
                workorder.status in
                <foreach collection="statusStage" item="element" open="(" close=")" separator=",">
                    #{element}
                </foreach>
            </if>
            <if test="type != null and type != ''">
                and workorder.type = #{type}
            </if>
            <if test="keyword != null and keyword != ''">
                and (task.code like '%' || #{keyword} || '%' or
                     user_is_name_like_by_id(workorder.organizer_id, #{keyword}))
            </if>
            <if test="processUserId != null and processUserId != ''">
                and workorder.id in
                (select distinct main_id
                 from work_order_details detail
                where detail.process_user_id = #{processUserId}
                  and detail.type in
                <foreach collection="onProcessStage" item="element" open="(" close=")" separator=",">
                    #{element}
                </foreach>
                )
            </if>
            and workorder.tenant_id = #{tenantId}
        </where>
    </select>

    <select id="workOrderCountByTypeTimed" resultType="org.thingsboard.server.dao.model.sql.statistic.StatisticLong">
        select w.type        as key,
               count(w.type) as value
        from sm_circuit_plan_work_order joint
                 left join work_order w
                           on w.serial_no = joint.order_code
        where create_time >= #{fromTime}
          and create_time &lt;= #{toTime}
          and w.tenant_id = #{tenantId}
        <if test="processUserId != null and processUserId != ''">
            and w.id in
            (select distinct main_id
             from work_order_details detail
            where detail.process_user_id = #{processUserId}
              and detail.type in
            <foreach collection="activeStageSet" item="element" open="(" close=")" separator=",">
                #{element}
            </foreach>
            )
        </if>
        group by w.type
    </select>

    <select id="workOrderCountByUserTimed" resultType="org.thingsboard.server.dao.model.sql.statistic.StatisticLong">
        select u.first_name          as key,
               count(w.organizer_id) as value
        from tb_user u
                 left join work_order w
                           on u.id = w.organizer_id
                 left join sm_circuit_plan_work_order joint
                           on w.serial_no = joint.order_code
        where w.create_time >= #{fromTime}
          and w.create_time &lt;= #{toTime}
          and w.tenant_id = #{tenantId}
        <if test="processUserId != null and processUserId != ''">
            and w.id in
            (select distinct main_id
             from work_order_details detail
            where detail.process_user_id = #{processUserId}
              and detail.type in
            <foreach collection="activeStageSet" item="element" open="(" close=")" separator=",">
                #{element}
            </foreach>
            )
        </if>
        group by w.organizer_id, u.first_name
    </select>

    <update id="complete">
        update sm_circuit_task
        set status = #{completeStatus}
        where id = #{taskId}
    </update>

    <update id="auditBatch">
        update sm_circuit_task
        <set>
            <if test="status != null and status != ''">
                status = #{status}
            </if>
            <if test="rejectReason != null and rejectReason != ''">
                ,reject_reason = #{rejectReason}
            </if>
        </set>
        where id in
        <foreach collection="taskIdList" item="element" open="(" close=")" separator=",">
            #{element}
        </foreach>
    </update>

    <select id="getBatchCode" resultType="java.lang.String">
        select code
        from sm_circuit_task where id in
        <foreach collection="list" item="element" open="(" close=")" separator=",">
            #{element}
        </foreach>
    </select>

    <select id="totalStatusOfUser" resultType="java.lang.Integer">
        select count(1)
        from sm_circuit_task
        where status in
        <foreach collection="status" open="(" close=")" item="element" separator=",">
            #{element}
        </foreach>
        <if test="userId != null and userId != ''">
            and (receive_user_id = #{userId} or str_like(collaborate_user_id, #{userId}))
        </if>
    </select>

    <select id="totalOfUser" resultType="java.lang.Integer">
        select count(1)
        from sm_circuit_task
        <if test="userId != null and userId != ''">
            where (receive_user_id = #{userId} or str_like(collaborate_user_id, #{userId}))
        </if>
    </select>

    <select id="getReportId" resultType="java.lang.String">
        select id
        from sm_circuit_task_report
        where task_code = #{code}
          and tenant_id = #{tenantId}
          and device_type = #{pointId}
    </select>

    <select id="isComplete" resultType="boolean">
        <bind name="completeStatus"
              value="@org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus@APPROVED"/>
        select status = #{completeStatus}
        from sm_circuit_task
        where code = #{code}
          and tenant_id = #{tenantId}
    </select>

    <select id="canBeComplete" resultType="boolean">
        select count(1) = 0
        from sm_circuit_task_report
        where task_code = (select code from sm_circuit_task where id = #{id})
          and tenant_id = (select tenant_id from sm_circuit_task where id = #{id})
          and is_settle
        <!--不需要反馈则直接为true，否则is_fallback必须为true才能达成条件-->
        and (is_fallback or (select is_need_feedback = false from sm_circuit_task))
    </select>

    <select id="countGisUser" resultType="java.lang.Integer">
        select count(distinct user_id)
        from customer_user_role
                 join (select regexp_split_to_table(roles, ',')
                       from tb_gis_people_setting
                       where type = #{type.value}
                         and tenant_id = #{tenantId}) xj_role(xj_role_id)
                      on customer_user_role.role_id = xj_role.xj_role_id
    </select>

    <select id="countOutsideCircuitUser" resultType="java.lang.Integer">
        <bind name="receiveStatus"
              value="@org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus@RECEIVED"/>
        select (select count(distinct receive_user_id)
                from sm_circuit_task
                         join (select distinct user_id
                               from customer_user_role
                                        join (select regexp_split_to_table(roles, ',')
                                              from tb_gis_people_setting
                                              where type = 'XUNJIANRENYUAN') xj_role(xj_role_id)
                                             on customer_user_role.role_id = xj_role.xj_role_id) xj_user(xj_user_id)
                              on receive_user_id = xj_user_id
                where status = #{receiveStatus}) +
               (select count(distinct collaborate_user_id)
                from sm_circuit_task
                         join (select distinct user_id
                               from customer_user_role
                                        join (select regexp_split_to_table(roles, ',')
                                              from tb_gis_people_setting
                                              where type = 'XUNJIANRENYUAN') xj_role(xj_role_id)
                                             on customer_user_role.role_id = xj_role.xj_role_id) xj_user(xj_user_id)
                              on collaborate_user_id = xj_user_id
                where status = #{receiveStatus})
    </select>

    <select id="countCustomerCount" resultType="java.lang.Integer">
        select count(1) from revenue.tb_cust_info
    </select>

    <select id="completeTotal" resultType="java.lang.Integer">
        select count(1) from sm_circuit_task where status = 'APPROVED'
    </select>

</mapper>