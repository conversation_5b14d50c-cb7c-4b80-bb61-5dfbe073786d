/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.telemetry;

import com.amazonaws.util.json.Jackson;
import com.google.gson.JsonParser;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.constantsAttribute.PropAttribute;
import org.thingsboard.server.common.data.dataMonitor.DataMonitor;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.kv.*;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.data.utils.AttributeConstants;
import org.thingsboard.server.common.data.utils.StringUtils;
import org.thingsboard.server.common.data.virtual.VirtualUtils;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.common.msg.session.SessionMsgType;
import org.thingsboard.server.common.transport.adaptor.JsonConverter;
import org.thingsboard.server.dao.model.ModelConstants;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Double.parseDouble;
import static org.thingsboard.server.common.data.virtual.VirtualUtils.isMathSymbol;

/**
 * 08/01
 * 根据创建遥测数据的属性来进行各种处理
 */
@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "遥测数据过滤",
        configClazz = TbMsgTimeseriesNodeConfiguration.class,
        nodeDescription = "对接收到的遥测数据进行过滤处理",
        nodeDetails = "对接收到的遥测数据进行处理，包含最大值过滤、最小值过滤、变比处理等",
        uiResources = {"static/rulenode/rulenode-core-config.js", "static/rulenode/rulenode-core-config.css"},
        configDirective = "tbActionNodeTimeseriesConfig",
        icon = "file_upload"
)
public class TbMsgTelemetryAttributeFilterNode implements TbNode {

    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {

    }

    /**
     * 获取数据并使用属性条件进行筛选
     *
     * @param ctx
     * @param msg
     */
    @Override
    public void onMsg(TbContext ctx, TbMsg msg) {
//        if (!msg.getType().equals(SessionMsgType.POST_TELEMETRY_REQUEST.name())) {
//            ctx.tellFailure(msg, new IllegalArgumentException("Unsupported msg type: " + msg.getType()));
//            return;
//        }
//        long ts = -1;
//        String tsStr = msg.getMetaData().getValue("ts");
//        if (!org.springframework.util.StringUtils.isEmpty(tsStr)) {
//            try {
//                ts = Long.parseLong(tsStr);
//            } catch (NumberFormatException e) {
//            }
//        } else {
//            ts = System.currentTimeMillis();
//        }
//        String src = msg.getData();
//        Map<Long, List<KvEntry>> tsKvMap = JsonConverter.convertToTelemetry(new JsonParser().parse(src), ts);
//        if (tsKvMap == null) {
//            ctx.tellFailure(msg, new IllegalArgumentException("Msg body is empty: " + src));
//            return;
//        }
//        List<TsKvEntry> tsKvEntryList = new ArrayList<>();
//        for (Map.Entry<Long, List<KvEntry>> tsKvEntry : tsKvMap.entrySet()) {
//            for (KvEntry kvEntry : tsKvEntry.getValue()) {
//                tsKvEntryList.add(new BasicTsKvEntry(tsKvEntry.getKey(), kvEntry));
//            }
//        }
//        List<TsKvEntry> resultList = filterData(ctx, msg, tsKvEntryList);
//        if (resultList != null && resultList.size() > 0) {
//            Map<String, String> map = new HashMap<>();
//            resultList.forEach(kvEntry -> map.put(kvEntry.getKey(), kvEntry.getValueAsString()));
//            msg.getMetaData().putData(resultList);
//            ctx.tellNext(new TbMsg(msg.getId(), msg.getType(), msg.getOriginator(), msg.getMetaData(), Jackson.toJsonString(map), msg.getRuleChainId(), msg.getRuleNodeId(), msg.getClusterPartition()), TbRelationTypes.SUCCESS);
//        } else {
//            ctx.tellNext(msg, TbRelationTypes.FAILURE);
//        }

    }

    @Override
    public void destroy() {
        log.info(this.getClass().getName() + "进行销毁" + System.currentTimeMillis());
    }

    /**
     * Filter all data based on criteria
     *
     * @param tbContext
     * @param tbMsg
     * @param kvEntrys
     * @return
     */
    private List<TsKvEntry> filterData(TbContext tbContext, TbMsg tbMsg, List<TsKvEntry> kvEntrys) {
        try {
            List<TsKvEntry> tsKvEntries = new ArrayList<>();
            TsKvEntry errorEntry = new BasicTsKvEntry(-1, null);
            Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(tbMsg.getMetaData().getValue(DataConstants.DEVICE_ID))));
            if (device.getGateWayId() != null) {
                Device gateway = tbContext.getDeviceService().findDeviceById(device.getGateWayId());
                if (DataConstants.PROTOCOL_TYPE_MODBUS.equals(gateway.getType())) {
                    return kvEntrys;
                }
            }

            //工控机上传数据源数据，直接返回
            if (device.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS) && device.getGateWayId() == null) {
                return kvEntrys;
            }
            AttributeKvEntry attributeKvEntry = tbContext.getAttributesService().findNotFuture(device.getId(), DataConstants.SHARED_SCOPE, ModelConstants.PROR);
            //当没取到attribute的数据时直接返回
            if (attributeKvEntry == null) {
                return kvEntrys;
            }
            List<PropAttribute> props = new ObjectMapper().readValue(attributeKvEntry.getValueAsString(), new TypeReference<List<PropAttribute>>() {
            });
            Collections.sort(props);
            Map<String, TsKvEntry> tsKvEntryMap = new HashMap<>();
            kvEntrys.forEach(p -> {
                tsKvEntryMap.put(p.getKey().toLowerCase(), p);
            });
            //获取设备的所有最后一次
            //针对每一个变量进行过滤
            long start3 = System.currentTimeMillis();
            List<TsKvEntry> result = props.stream().map(propAttribute -> {
                try {
                    if (tsKvEntryMap.containsKey(propAttribute.getPropertyCategory().toLowerCase())) {
                        TsKvEntry k = tsKvEntryMap.get(propAttribute.getPropertyCategory().toLowerCase());
                        //原始数据
                        BigDecimal value = new BigDecimal(k.getValueAsString());
                        // 根据数据的类型来对属性的采样系数和最大值，最小值进行值过滤
                        // 2019-01-24添加对公式的处理，数据入栈规则为采样系数，公式，极值判断
                        switch (k.getDataType()) {
                            case STRING:
                                return k;
                            case DOUBLE:
                            case LONG:
                                try {
                                    if (StringUtils.checkNotNull(propAttribute.getSampleCoef())) {
                                        value = value.multiply(new BigDecimal(propAttribute.getSampleCoef()));
                                    }
                                    if (StringUtils.checkNotNull(propAttribute.getFormulaProperty()) && !propAttribute.getFormulaProperty().equals(DataConstants.FORMULA_SELF)) {
                                        value = covertByFormula(tbContext, propAttribute.getFormulaProperty(), value, k.getKey(), tsKvEntries, UUIDConverter.fromTimeUUID(device.getUuidId()));
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    log.info("指数运算失败，公式：" + propAttribute.getFormulaProperty() + "异常" + e.getLocalizedMessage());
                                }
                                //根据单位系数处理数据的小数位数
                                if (StringUtils.checkNotNull(propAttribute.getUnitCoef()) && !propAttribute.getUnitCoef().equalsIgnoreCase("-1")) {
                                    value = value.setScale(Integer.parseInt(propAttribute.getUnitCoef()), BigDecimal.ROUND_HALF_UP);
                                }
                                TsKvEntry kvEntry = new BasicTsKvEntry(k.getTs(), new StringDataEntry(k.getKey(), value.toString()));
                                // 对数据最大值和最小值进行过滤，即当经过采集系数和公式处理后的数据大于采样最大值或者小于采样最小值时将返回空值
                                if (processMaxAndMin(kvEntry, propAttribute)) {
                                    // 保存错误值到数据库(暂不需要)
//                                    tbContext.getDataMonitorService().saveDataMonitor(new DataMonitor(device.getTenantId(), device.getId(), System.currentTimeMillis(), propAttribute.getName(), kvEntry.getValueAsString(), "数据超出最大值或小于最小值！"));
                                    return errorEntry;
                                }
                                // 处理无效值
                                if (processInvalidValue(kvEntry, propAttribute)) {
                                    return errorEntry;
                                }
                                // 判断当前值是否超过偏差值
                                /*TsKvEntry tsKvEntry = tbContext.getTimeseriesService().findLatestByKey(device.getId(), kvEntry.getKey());
                                //log.info("获取最后一次数据花费时间"+(System.currentTimeMillis()-start));
                                if (tsKvEntry != null && tsKvEntry.getValueAsString() != null) {
                                    if (processDeviation(kvEntry, propAttribute, tsKvEntry)) {
                                        //新增量程逻辑：当上一次采集数据大于量程数据，且当采集数据小于上一条数据时判定为可能是量程归零，发送短信提醒用户
                                        if (processRange(kvEntry, propAttribute, tsKvEntry)) {
                                            tbContext.getMailService().sendRangeMsg(device, kvEntry, propAttribute);
                                        } else if (System.currentTimeMillis() % (1000 * 60) == 0) {
                                            tbContext.getDataMonitorService().saveDataMonitor(new DataMonitor(device.getTenantId(), device.getId(), System.currentTimeMillis(), propAttribute.getName(), kvEntry.getValueAsString(), "数据超出偏差值！"));
                                            return errorEntry;
                                        } else {
                                            return errorEntry;
                                        }
                                    }
                                }*/
                                tsKvEntries.add(kvEntry);
                                return kvEntry;
                            case BOOLEAN:
                                return k;
                            default:
                                return k;
                        }
                    } else {
                        //检查是否是虚拟变量
                        if (propAttribute.getIsVirtual() != null && propAttribute.getIsVirtual().equalsIgnoreCase(DataConstants.TRUE)) {
                            BigDecimal value = covertByFormula(tbContext, propAttribute.getFormulaProperty(), new BigDecimal(0), UUIDConverter.fromTimeUUID(device.getUuidId()) + "." + propAttribute.getPropertyCategory(), tsKvEntries, UUIDConverter.fromTimeUUID(device.getUuidId()));
                            TsKvEntry kvEntry = new BasicTsKvEntry(tsKvEntries.get(0).getTs(), new DoubleDataEntry(propAttribute.getPropertyCategory(), value.doubleValue()));
                            tsKvEntries.add(kvEntry);
                            return kvEntry;
                        }
                        return errorEntry;
                    }
                } catch (Exception e) {
                    return errorEntry;
                }
            }).collect(Collectors.toList());
            //检查是否存在虚拟变量，如果存在则进行虚拟变量的数据处理
            result = result.stream().filter(k -> k.getTs() != -1).collect(Collectors.toList());
            log.info("数据过滤耗时：{}", System.currentTimeMillis() - start3);
            return result;
        } catch (Exception e) {
            //log.info("未查找到属性，数据过滤失败");
            //e.printStackTrace();
            return null;
        }

    }

    /**
     * 无效值处理
     */
    private boolean processInvalidValue(TsKvEntry kvEntry, PropAttribute propAttribute) {
        if (!StringUtils.checkNotNull(propAttribute.getInvalidValue())) {
            return false;
        }
        return new BigDecimal("-120").doubleValue() == (new BigDecimal("-120.0").doubleValue());
    }

    /**
     * 最大最小值处理
     */
    private boolean processMaxAndMin(TsKvEntry kvEntry, PropAttribute propAttribute) {
        if ((StringUtils.checkNotNull(propAttribute.getSamplingMin()) && parseDouble(kvEntry.getValueAsString()) < parseDouble(propAttribute.getSamplingMin())) ||
                (StringUtils.checkNotNull(propAttribute.getSamplingMax()) && parseDouble(kvEntry.getValueAsString()) > parseDouble(propAttribute.getSamplingMax()))) {
            return true;
        }
        return false;
    }


    /**
     * 量程处理
     */
    private boolean processRange(TsKvEntry kvEntry, PropAttribute propAttribute, TsKvEntry lastData) {
        if (propAttribute.getRange() != null && !propAttribute.getRange().equals("") && !propAttribute.getRange().equals("0")) {
            //上次采集数据大于等于量程值
            if (new BigDecimal(lastData.getValueAsString()).compareTo(new BigDecimal(propAttribute.getRange())) > -1) {
                if (new BigDecimal(lastData.getValueAsString()).multiply(new BigDecimal("0.1")).compareTo(new BigDecimal(kvEntry.getValueAsString())) == 1) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 偏差值处理
     */
    private boolean processDeviation(TsKvEntry kvEntry, PropAttribute propAttribute, TsKvEntry lastData) {
        // 判断当前值是否超过偏差值
        if (StringUtils.checkNotNull(propAttribute.getSampleDeviation()) && lastData != null && lastData.getValueAsString() != null
                && Long.parseLong(propAttribute.getSampleDeviation()) > 0) {
            // 计算两次数据时间差，单位为小时,向上取整不足一小时按一小时计算
            BigDecimal time = (System.currentTimeMillis() - lastData.getTs()) < 3600000 ?
                    BigDecimal.ONE : BigDecimal.valueOf((System.currentTimeMillis() - lastData.getTs()) / 3600000).setScale(0, BigDecimal.ROUND_UP);
            // 差值
            BigDecimal subtractValue = new BigDecimal(kvEntry.getValueAsString()).subtract(new BigDecimal(lastData.getValueAsString()));
            BigDecimal deviationMax = time.multiply(new BigDecimal(propAttribute.getSampleDeviation()));
            // 判断值是否超过偏差界限
            if (propAttribute.getStatType().equals(AttributeConstants.TELEMETRY_DATA_TYPE_RANDOM)) {
                // 瞬时值，判断绝对值
                BigDecimal abs = subtractValue.abs();
                if (abs.compareTo(deviationMax) > 0) {
                    return true;
                }
            } else {
                // 累计值
                if (subtractValue.compareTo(BigDecimal.ZERO) < 0 || subtractValue.compareTo(deviationMax) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 根据公式对数值进行计算
     *
     * @param formula
     * @param value
     */
    private BigDecimal covertByFormula(TbContext tbContext, String formula, BigDecimal value, String
            name, List<TsKvEntry> tsKvEntries, String deviceId) {
        //先判断是否带有指数运算
        List<String> formulas = VirtualUtils.handleVirtualFormula(formula);
        if (formulas != null) {
            List<String> result = new ArrayList<>();
            formulas.forEach(fo -> {
                if (!isMathSymbol(fo) && !result.contains(fo)) {
                    result.add(fo);
                }
            });
            //把已经计算好的数据进行排列
            Map<String, TsKvEntry> map = new HashMap<>();
            if (tsKvEntries != null && tsKvEntries.size() > 0) {
                tsKvEntries.forEach(t -> {
                    if (t != null && t.getKey() != null) {
                        map.put(deviceId + "." + t.getKey().toLowerCase(), t);
                    }
                });
            }

            Map<String, String> params = new HashMap<>();
            //对属性值的计算
            if (result.size() > 0) {
                result.forEach(f -> {
                    if (f.contains("^")) {
                        //指数运算
                        params.put(f, covertByIndex(tbContext, f, map));
                    } else if (f.equals(name)) {
                        //引用telemetry值
                        params.put(f, String.valueOf(value));
                    } else if (map.containsKey(f.toLowerCase())) {
                        params.put(f, map.get(f.toLowerCase()).getValueAsString());
                    } else {
                        //此处不包含指数运算的属性计算
                        String[] param = f.split("\\.");
                        List<TsKvEntry> results = null;
                        try {
                            results = tbContext.getTimeseriesService().findLatest(new DeviceId(UUIDConverter.fromString(param[0])), Collections.singletonList(param[1])).get();
                            //此处查询只会查询一次，所以直接取第一个
                            if (results != null && results.size() > 0) {
                                params.put(f, results.get(0).getValueAsString());
                            }
                        } catch (Exception e) {
                            //e.printStackTrace();
                            log.error("获取最后一次数据失败，设备ID为：");
                        }
                    }
                });
                //替换掉公式中的属性值
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    formula = formula.replace(entry.getKey(), entry.getValue());
                }
                formula.replace(name, String.valueOf(value));
            }
            //计算并返回
            return new BigDecimal(AviatorEvaluator.execute(formula).toString());
        } else {
            return value;
        }
    }

    /**
     * 指数计算
     *
     * @param tbContext
     * @param indexFormula
     */
    private String covertByIndex(TbContext tbContext, String indexFormula, Map<String, TsKvEntry> tsKvEntryMap) {
        String string = "0";
        try {
            String[] params = indexFormula.split(DataConstants.INDEX);
            string = String.valueOf(Math.pow(Double.parseDouble(getAttributeValue(tbContext, params[0], tsKvEntryMap)), Double.parseDouble(getAttributeValue(tbContext, params[1], tsKvEntryMap))));
        } catch (Exception e) {
            log.error("指数运算出现错误！公式为" + indexFormula + "，错误信息：" + e.getMessage());
        }
        return string;
    }

    private String getAttributeValue(TbContext tbContext, String formula, Map<String, TsKvEntry> map) {
        try {
            if (!isMathSymbol(formula)) {
                TsKvEntry tsKvEntry = null;
                if (map.containsKey(formula)) {
                    tsKvEntry = map.get(formula);
                } else {
                    String[] params = formula.split("\\.");
                    List<TsKvEntry> results = tbContext.getTimeseriesService().findLatest(new DeviceId(UUIDConverter.fromString(params[0])), Collections.singletonList(params[1])).get();
                    //此处查询只会查询一次，所以直接取第一个
                    if (results != null && results.size() > 0) {
                        tsKvEntry = results.get(0);
                    }
                }
                return tsKvEntry.getValueAsString();
            } else {
                return formula;
            }
        } catch (Exception e) {
            return "0";
        }
    }


}
