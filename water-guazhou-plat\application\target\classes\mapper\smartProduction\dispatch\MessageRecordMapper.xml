<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.MessageRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           receive_user_id,
                           content,
                           receive_phone,
                           send_user_id,
                           send_time,
                           status,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_message_record -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecord">
        <result column="id" property="id"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="content" property="content"/>
        <result column="receive_phone" property="receivePhone"/>
        <result column="send_user_id" property="sendUserId"/>
        <result column="send_time" property="sendTime"/>
        <result column="status" property="status"
                typeHandler="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageRecordStatusTypeHandler"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_message_record
        <where>
            <if test="content != null and content != ''">
                and str_like(content, #{content})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <insert id="send">
        insert into sp_message_record(id,
                                      receive_user_id,
                                      template_id,
                                      content,
                                      receive_phone,
                                      send_user_id,
                                      send_time,
                                      status,
                                      variables,
                                      is_reliable,
                                      creator,
                                      create_time,
                                      tenant_id)
        values
        <foreach collection="list" open="" close="" item="element" separator=",">
            (#{element.id},
             #{element.receiveUserId},
             #{element.templateId},
             (select tmp.content from sp_message_template tmp where tmp.id = #{element.templateId}),
             #{element.receiveUserPhone},
             #{element.sendUserId},
            <choose>
                <when test="autoDate">
                    <!--@ignoreSql-->
                    now(),
                </when>
                <otherwise>
                    #{element.sendTime},
                </otherwise>
            </choose>
            #{element.status},
            #{element.variables},
            #{reliable},
            #{element.creator},
            now(),
            #{element.tenantId})
        </foreach>
    </insert>

    <update id="markStatus">
        update sp_message_record
        set status = #{status}
        where id = #{id}
    </update>

    <resultMap id="MessageQueueMessageRecordSendRequestInfoResultMap"
               type="org.thingsboard.server.dao.util.imodel.query.smartProduction.dispatch.MessageQueueMessageRecordSendRequestInfo">
        <id column="id" property="id"/>
        <result column="receive_user_phone" property="receiveUserPhone"/>
        <result column="code" property="code"/>
        <result column="sign_key" property="signKey"/>
        <result column="variables" property="variables"/>
    </resultMap>
    <select id="getReadyMessagesByArrangedStatus"
            resultMap="MessageQueueMessageRecordSendRequestInfoResultMap">
        select id,
               receive_phone                                                         receive_user_phone,
               (select t.code from sp_message_template t where id = template_id)     code,
               (select t.sign_key from sp_message_template t where id = template_id) sign_key,
               variables
        from sp_message_record
        where send_time is not null
          and send_time &lt;= now()
          and (
                    status = #{pendingStatus} or
                    (is_reliable and status != #{sendingStatus} and status != #{successStatus})
            )
    </select>

    <update id="markBatchStatus">
        update sp_message_record
        set status = #{status}
        where id in
        <foreach collection="list" open="(" close=")" item="element" separator=",">
            #{element}
        </foreach>
    </update>
</mapper>