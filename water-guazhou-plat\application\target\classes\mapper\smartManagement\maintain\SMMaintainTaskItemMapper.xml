<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.maintain.SMMaintainTaskItemMapper">
    <sql id="Base_Column_List">
        id,
        task_id,
        object_id,
        img,
        audio,
        video,
        file,
        complete_time,
        remark,
        tenant_id
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.maintaince.SMMaintainTaskItem">
        <result column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="object_id" property="objectId"/>
        <result column="img" property="img"/>
        <result column="audio" property="audio"/>
        <result column="video" property="video"/>
        <result column="file" property="file"/>
        <result column="complete_time" property="completeTime"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <insert id="saveAll">
        INSERT INTO sm_maintain_task_item(id,
                                          task_id,
                                          object_id,
                                          img,
                                          audio,
                                          video,
                                          file,
                                          complete_time,
                                          remark,
                                          tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.taskId},
             #{element.objectId},
             #{element.img},
             #{element.audio},
             #{element.video},
             #{element.file},
             #{element.completeTime},
             #{element.remark},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="update">
        update sm_maintain_task_item
        <set>
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="objectId != null">
                object_id = #{objectId},
            </if>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="audio != null">
                audio = #{audio},
            </if>
            <if test="video != null">
                video = #{video},
            </if>
            <if test="file != null">
                file = #{file},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_maintain_task_item
        <where>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId}
            </if>
            <if test="isComplete != null">
                <choose>
                    <when test="isComplete">
                        and complete_time is not null
                    </when>
                    <otherwise>
                        and complete_time is null
                    </otherwise>
                </choose>
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="report">
        update sm_maintain_task_item
        set complete_time = now(),
            img           = #{img},
            audio         = #{audio},
            video         = #{video},
            file          = #{file},
            remark        = #{remark}
        where id = #{taskItemId}
    </update>

    <delete id="removeAllByTaskId">
        delete
        from sm_maintain_task_item
        where task_id = #{id}
    </delete>
</mapper>