<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.TellAreaCodeInfoMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.TellAreaCodeInfo">
        select a.*
        from tb_service_call_tel_area_code_info a
        where id like '%'||#{keywords}||'%' or a.area_name like '%'||#{keywords}||'%'
        order by a.id
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_tel_area_code_info a
        where a.id like '%'||#{keywords}||'%' or a.area_name like '%'||#{keywords}||'%'
    </select>
</mapper>