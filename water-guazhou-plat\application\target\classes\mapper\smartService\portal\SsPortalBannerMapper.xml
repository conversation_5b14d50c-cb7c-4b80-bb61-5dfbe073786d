<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalBannerMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        image,
        active,
        order_num,
        create_time,
        tenant_id
        <!--@sql from ss_portal_banner -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBanner">
        <result column="id" property="id"/>
        <result column="image" property="image"/>
        <result column="active" property="active"/>
        <result column="order_num" property="orderNum"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_banner
        <where>
            <if test="active != null">
                and active = #{active}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>

    <update id="updateFully">
        update ss_portal_banner
        set image     = #{image},
            order_num = #{orderNum}
        where id = #{id}
    </update>

    <update id="active">
        update ss_portal_banner
        set active = #{active}
        where id = #{id}
    </update>
</mapper>