-- 事件总览功能增强SQL脚本
-- 为工单表添加坐标相关字段（如果不存在）

-- 检查并添加坐标字段
DO $$
BEGIN
    -- 检查coordinate字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'work_orders' 
        AND column_name = 'coordinate'
    ) THEN
        -- 添加坐标字段
        ALTER TABLE work_orders ADD COLUMN coordinate VARCHAR(50) COMMENT '地理位置坐标，格式：经度,纬度';
    END IF;
    
    -- 检查coordinate_name字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'work_orders' 
        AND column_name = 'coordinate_name'
    ) THEN
        -- 添加坐标名称字段
        ALTER TABLE work_orders ADD COLUMN coordinate_name VARCHAR(200) COMMENT '地理位置名称';
    END IF;
END $$;

-- 创建事件总览相关索引（提高查询性能）
CREATE INDEX IF NOT EXISTS idx_work_orders_create_time ON work_orders(create_time);
CREATE INDEX IF NOT EXISTS idx_work_orders_status ON work_orders(status);
CREATE INDEX IF NOT EXISTS idx_work_orders_source ON work_orders(source);
CREATE INDEX IF NOT EXISTS idx_work_orders_organizer_id ON work_orders(organizer_id);
CREATE INDEX IF NOT EXISTS idx_work_orders_tenant_id ON work_orders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_work_orders_coordinate ON work_orders(coordinate);

-- 创建复合索引（用于事件总览查询优化）
CREATE INDEX IF NOT EXISTS idx_work_orders_tenant_time_status 
ON work_orders(tenant_id, create_time, status);

-- 为地址字段创建全文索引（支持地址搜索）
CREATE INDEX IF NOT EXISTS idx_work_orders_address_gin 
ON work_orders USING gin(to_tsvector('simple', COALESCE(address, '') || ' ' || COALESCE(upload_address, '')));

-- 插入一些示例坐标数据（可选，用于测试）
-- 注意：这里使用的是北京地区的坐标范围
UPDATE work_orders 
SET coordinate = CONCAT(
    ROUND(116.3 + RANDOM() * 0.4, 6), 
    ',', 
    ROUND(39.8 + RANDOM() * 0.4, 6)
)
WHERE coordinate IS NULL 
AND address IS NOT NULL 
AND address != ''
AND tenant_id IS NOT NULL;

-- 创建事件统计视图（可选）
CREATE OR REPLACE VIEW v_event_overview_statistics AS
SELECT 
    tenant_id,
    COUNT(*) as total_events,
    COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_events,
    COUNT(CASE WHEN status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_events,
    COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_events,
    COUNT(CASE WHEN status IN ('TERMINATED', 'REJECTED') THEN 1 END) as terminated_events,
    COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) as today_new,
    COUNT(CASE WHEN create_time >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as week_new,
    COUNT(CASE WHEN create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as month_new,
    COUNT(CASE WHEN coordinate IS NOT NULL AND coordinate != '' THEN 1 END) as events_with_location
FROM work_orders
WHERE tenant_id IS NOT NULL
GROUP BY tenant_id;

-- 创建热点地区统计视图（可选）
CREATE OR REPLACE VIEW v_event_hotspot_statistics AS
SELECT 
    tenant_id,
    SUBSTRING(address, 1, 20) as area_name,
    COUNT(*) as event_count,
    COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,
    AVG(CASE 
        WHEN complete_time IS NOT NULL AND create_time IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (complete_time - create_time))/3600 
    END) as avg_process_hours
FROM work_orders
WHERE tenant_id IS NOT NULL 
AND address IS NOT NULL 
AND address != ''
GROUP BY tenant_id, SUBSTRING(address, 1, 20)
HAVING COUNT(*) >= 2
ORDER BY event_count DESC;

-- 添加注释
COMMENT ON COLUMN work_orders.coordinate IS '地理位置坐标，格式：经度,纬度，例如：116.397428,39.90923';
COMMENT ON COLUMN work_orders.coordinate_name IS '地理位置名称，用于显示位置的友好名称';

-- 创建事件总览功能相关的存储过程（可选）
CREATE OR REPLACE FUNCTION get_event_statistics(
    p_tenant_id VARCHAR,
    p_from_time TIMESTAMP DEFAULT NULL,
    p_to_time TIMESTAMP DEFAULT NULL,
    p_source VARCHAR DEFAULT NULL
)
RETURNS TABLE(
    total_count BIGINT,
    pending_count BIGINT,
    processing_count BIGINT,
    completed_count BIGINT,
    terminated_count BIGINT,
    today_new_count BIGINT,
    week_new_count BIGINT,
    month_new_count BIGINT,
    avg_process_hours NUMERIC,
    timely_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN wo.status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,
        COUNT(CASE WHEN wo.status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_count,
        COUNT(CASE WHEN wo.status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,
        COUNT(CASE WHEN wo.status IN ('TERMINATED', 'REJECTED') THEN 1 END) as terminated_count,
        COUNT(CASE WHEN DATE(wo.create_time) = CURRENT_DATE THEN 1 END) as today_new_count,
        COUNT(CASE WHEN wo.create_time >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as week_new_count,
        COUNT(CASE WHEN wo.create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as month_new_count,
        AVG(CASE 
            WHEN wo.complete_time IS NOT NULL AND wo.create_time IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (wo.complete_time - wo.create_time))/3600 
        END)::NUMERIC(10,2) as avg_process_hours,
        (COUNT(CASE 
            WHEN wo.complete_time IS NOT NULL 
            AND wo.estimated_finish_time IS NOT NULL 
            AND wo.complete_time <= wo.estimated_finish_time 
            THEN 1 
        END) * 100.0 / NULLIF(COUNT(CASE WHEN wo.complete_time IS NOT NULL THEN 1 END), 0))::NUMERIC(5,2) as timely_rate
    FROM work_orders wo
    WHERE wo.tenant_id = p_tenant_id
    AND (p_from_time IS NULL OR wo.create_time >= p_from_time)
    AND (p_to_time IS NULL OR wo.create_time <= p_to_time)
    AND (p_source IS NULL OR wo.source = p_source);
END;
$$ LANGUAGE plpgsql;
