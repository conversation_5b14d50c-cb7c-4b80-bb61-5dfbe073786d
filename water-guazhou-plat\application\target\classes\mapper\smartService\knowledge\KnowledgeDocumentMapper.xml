<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.knowledge.KnowledgeDocumentMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.knowledge.KnowledgeDocument">
        select a.*, b.name as typeName
        from tb_service_knowledge_document a
        left join tb_service_knowledge_base_type b on a.type_id = b.id

        where a.tenant_id = #{tenantId}
        <if test="typeIds.size() > 0">
            and b.id in
            <foreach collection="typeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
         <if test="name != null and name != ''">
             and a.name like '%'||#{name}||'%'
         </if>
         order by a.create_time desc
    </select>
</mapper>