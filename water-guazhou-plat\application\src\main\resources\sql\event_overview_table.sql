-- 事件总览功能数据库表创建脚本
-- 注意：由于事件总览基于现有的work_orders表，这里主要是确保表结构完整和添加必要的索引

-- 检查work_orders表是否存在coordinate和coordinate_name字段，如果不存在则添加
DO $$
BEGIN
    -- 检查coordinate字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'work_orders' 
        AND column_name = 'coordinate'
    ) THEN
        -- 添加坐标字段
        ALTER TABLE work_orders ADD COLUMN coordinate VARCHAR(50);
        COMMENT ON COLUMN work_orders.coordinate IS '地理位置坐标，格式：经度,纬度';
    END IF;
    
    -- 检查coordinate_name字段是否存在
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'work_orders' 
        AND column_name = 'coordinate_name'
    ) THEN
        -- 添加坐标名称字段
        ALTER TABLE work_orders ADD COLUMN coordinate_name VARCHAR(200);
        COMMENT ON COLUMN work_orders.coordinate_name IS '地理位置名称';
    END IF;
END $$;




-- 创建事件统计视图（可选，用于快速统计查询）
CREATE OR REPLACE VIEW v_event_overview_statistics AS
SELECT 
    tenant_id,
    project_id,
    COUNT(*) as total_events,
    COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_events,
    COUNT(CASE WHEN status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_events,
    COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_events,
    COUNT(CASE WHEN status IN ('TERMINATED', 'REJECTED') THEN 1 END) as terminated_events,
    COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) as today_new,
    COUNT(CASE WHEN create_time >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as week_new,
    COUNT(CASE WHEN create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as month_new,
    COUNT(CASE WHEN coordinate IS NOT NULL AND coordinate != '' THEN 1 END) as events_with_location,
    AVG(CASE 
        WHEN complete_time IS NOT NULL AND create_time IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (complete_time - create_time))/3600 
    END) as avg_process_hours,
    COUNT(CASE 
        WHEN estimated_finish_time IS NOT NULL 
        AND (complete_time IS NULL OR complete_time > estimated_finish_time)
        AND status NOT IN ('COMPLETE', 'APPROVED', 'TERMINATED', 'REJECTED')
        THEN 1 
    END) as overdue_events
FROM work_orders
WHERE tenant_id IS NOT NULL
GROUP BY tenant_id, project_id;

-- 创建热点地区统计视图（可选）
CREATE OR REPLACE VIEW v_event_hotspot_statistics AS
SELECT 
    tenant_id,
    SUBSTRING(address, 1, 20) as area_name,
    COUNT(*) as event_count,
    COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,
    COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,
    AVG(CASE 
        WHEN complete_time IS NOT NULL AND create_time IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (complete_time - create_time))/3600 
    END) as avg_process_hours,
    MAX(create_time) as latest_event_time
FROM work_orders
WHERE tenant_id IS NOT NULL 
AND address IS NOT NULL 
AND address != ''
GROUP BY tenant_id, SUBSTRING(address, 1, 20)
HAVING COUNT(*) >= 2
ORDER BY event_count DESC;

-- 创建事件趋势统计视图（可选）
CREATE OR REPLACE VIEW v_event_trend_statistics AS
SELECT 
    tenant_id,
    DATE(create_time) as event_date,
    COUNT(*) as event_count,
    COUNT(CASE WHEN status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,
    COUNT(CASE WHEN status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_count,
    COUNT(CASE WHEN status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,
    COUNT(CASE WHEN level IN ('高', 'HIGH') THEN 1 END) as high_priority_count
FROM work_orders
WHERE tenant_id IS NOT NULL 
AND create_time >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY tenant_id, DATE(create_time)
ORDER BY tenant_id, event_date DESC;

-- 添加表注释
COMMENT ON TABLE work_orders IS '工单表，用于事件总览功能的数据源';

-- 添加字段注释（如果还没有的话）
DO $$
BEGIN
    -- 为主要字段添加注释
    IF NOT EXISTS (
        SELECT 1 FROM pg_description d 
        JOIN pg_class c ON d.objoid = c.oid 
        JOIN pg_attribute a ON d.objoid = a.attrelid AND d.objsubid = a.attnum
        WHERE c.relname = 'work_orders' AND a.attname = 'serial_no'
    ) THEN
        COMMENT ON COLUMN work_orders.serial_no IS '工单编号，唯一标识';
        COMMENT ON COLUMN work_orders.title IS '事件标题';
        COMMENT ON COLUMN work_orders.source IS '事件来源';
        COMMENT ON COLUMN work_orders.type IS '事件类型';
        COMMENT ON COLUMN work_orders.level IS '紧急程度';
        COMMENT ON COLUMN work_orders.address IS '发生地点';
        COMMENT ON COLUMN work_orders.upload_address IS '上报地点';
        COMMENT ON COLUMN work_orders.remark IS '事件描述';
        COMMENT ON COLUMN work_orders.status IS '事件状态';
        COMMENT ON COLUMN work_orders.organizer_id IS '创建人ID';
        COMMENT ON COLUMN work_orders.process_user_id IS '处理人ID';
        COMMENT ON COLUMN work_orders.step_process_user_id IS '当前步骤处理人ID';
        COMMENT ON COLUMN work_orders.create_time IS '创建时间';
        COMMENT ON COLUMN work_orders.update_time IS '更新时间';
        COMMENT ON COLUMN work_orders.complete_time IS '完成时间';
        COMMENT ON COLUMN work_orders.estimated_finish_time IS '预计完成时间';
        COMMENT ON COLUMN work_orders.tenant_id IS '租户ID';
        COMMENT ON COLUMN work_orders.project_id IS '项目ID';
    END IF;
END $$;

-- 创建事件总览功能相关的存储过程（可选）
CREATE OR REPLACE FUNCTION get_event_overview_statistics(
    p_tenant_id VARCHAR,
    p_project_id VARCHAR DEFAULT NULL,
    p_from_time TIMESTAMP DEFAULT NULL,
    p_to_time TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    total_count BIGINT,
    pending_count BIGINT,
    processing_count BIGINT,
    completed_count BIGINT,
    terminated_count BIGINT,
    today_new_count BIGINT,
    week_new_count BIGINT,
    month_new_count BIGINT,
    events_with_location_count BIGINT,
    avg_process_hours NUMERIC,
    overdue_count BIGINT,
    high_priority_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN wo.status IN ('PENDING', 'ASSIGN') THEN 1 END) as pending_count,
        COUNT(CASE WHEN wo.status IN ('RESOLVING', 'ARRIVING', 'PROCESSING') THEN 1 END) as processing_count,
        COUNT(CASE WHEN wo.status IN ('COMPLETE', 'APPROVED') THEN 1 END) as completed_count,
        COUNT(CASE WHEN wo.status IN ('TERMINATED', 'REJECTED') THEN 1 END) as terminated_count,
        COUNT(CASE WHEN DATE(wo.create_time) = CURRENT_DATE THEN 1 END) as today_new_count,
        COUNT(CASE WHEN wo.create_time >= DATE_TRUNC('week', CURRENT_DATE) THEN 1 END) as week_new_count,
        COUNT(CASE WHEN wo.create_time >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as month_new_count,
        COUNT(CASE WHEN wo.coordinate IS NOT NULL AND wo.coordinate != '' THEN 1 END) as events_with_location_count,
        AVG(CASE 
            WHEN wo.complete_time IS NOT NULL AND wo.create_time IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (wo.complete_time - wo.create_time))/3600 
        END)::NUMERIC(10,2) as avg_process_hours,
        COUNT(CASE 
            WHEN wo.estimated_finish_time IS NOT NULL 
            AND (wo.complete_time IS NULL OR wo.complete_time > wo.estimated_finish_time)
            AND wo.status NOT IN ('COMPLETE', 'APPROVED', 'TERMINATED', 'REJECTED')
            THEN 1 
        END) as overdue_count,
        COUNT(CASE WHEN wo.level IN ('高', 'HIGH') THEN 1 END) as high_priority_count
    FROM work_orders wo
    WHERE wo.tenant_id = p_tenant_id
    AND (p_project_id IS NULL OR wo.project_id = p_project_id)
    AND (p_from_time IS NULL OR wo.create_time >= p_from_time)
    AND (p_to_time IS NULL OR wo.create_time <= p_to_time);
END;
$$ LANGUAGE plpgsql;

-- 创建清理过期数据的存储过程（可选）
CREATE OR REPLACE FUNCTION cleanup_old_event_data(
    p_tenant_id VARCHAR,
    p_days_to_keep INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除指定天数之前的已完成事件数据（保留重要数据）
    DELETE FROM work_orders 
    WHERE tenant_id = p_tenant_id
    AND status IN ('COMPLETE', 'APPROVED', 'TERMINATED')
    AND create_time < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep
    AND level NOT IN ('高', 'HIGH'); -- 保留高优先级事件
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 插入一些示例坐标数据（可选，用于测试）
-- 注意：这里使用的是中国常见城市的坐标范围
/*
UPDATE work_orders 
SET coordinate = CONCAT(
    ROUND(116.3 + RANDOM() * 0.4, 6), 
    ',', 
    ROUND(39.8 + RANDOM() * 0.4, 6)
),
coordinate_name = '北京市区域'
WHERE coordinate IS NULL 
AND address IS NOT NULL 
AND address != ''
AND tenant_id IS NOT NULL
AND RANDOM() < 0.3; -- 只为30%的记录添加坐标
*/
