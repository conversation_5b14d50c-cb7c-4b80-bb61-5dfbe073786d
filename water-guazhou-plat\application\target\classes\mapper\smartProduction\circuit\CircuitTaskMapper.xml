<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.circuit.CircuitTaskMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           type,
                           code,
                           name,
                           task_type,
                           execution_user_id,
                           audit_user_id,
                           start_time,
                           end_time,
                           real_start_time,
                           real_end_time,
                           station_id,
                           template_id,
                           status,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_circuit_task -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.circuit.CircuitTask">
        <result column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="task_type" property="taskType"/>
        <result column="execution_user_id" property="executionUserId"/>
        <result column="audit_user_id" property="auditUserId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="real_start_time" property="realStartTime"/>
        <result column="real_end_time" property="realEndTime"/>
        <result column="station_id" property="stationId"/>
        <result column="template_id" property="templateId"/>
        <result column="status" property="status"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_circuit_task
        <where>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="code != null and code != ''">
                and code = #{code}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="taskType != null and taskType != ''">
                and task_type = #{taskType}
            </if>
            <if test="(executionUserId == null || executionUserId == '') and executionUserDepartmentId != null and executionUserDepartmentId != ''">
                and is_user_at_department(execution_user_id, #{executionUserDepartmentId})
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                and execution_user_id = #{executionUserId}
            </if>
            <if test="auditUserId != null and auditUserId != ''">
                and audit_user_id = #{auditUserId}
            </if>
            <if test="startTime != null">
                and onday(start_time, #{startTime})
            </if>
            <if test="endTime != null">
                and onday(end_time, #{endTime})
            </if>
            <if test="realStartTime != null">
                and onday(real_start_time, #{realStartTime})
            </if>
            <if test="realEndTime != null">
                and onday(real_end_time, #{realEndTime})
            </if>
            <if test="stationId != null and stationId != ''">
                and
                (<trim prefixOverrides="or">
                    <foreach collection="stationId.split(',')" item="element">
                    or station_id like '%'|| #{element} ||'%'
                </foreach>
                </trim>)
            </if>
            <if test="templateId != null and templateId != ''">
                and template_id = #{templateId}
            </if>
            <if test="creator != null and creator != ''">
                and creator = #{creator}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_circuit_task
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="executionUserId != null">
                execution_user_id = #{executionUserId},
            </if>
            <if test="auditUserId != null">
                audit_user_id = #{auditUserId},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="realStartTime != null">
                real_start_time = #{realStartTime},
            </if>
            <if test="realEndTime != null">
                real_end_time = #{realEndTime},
            </if>
            <if test="stationId != null">
                station_id = #{stationId},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="verify">
        update sp_circuit_task
        <set>
            <choose>
                <when test="allow">
                    status = 'APPROVED',
                </when>
                <otherwise>
                    status = 'REJECTED',
                </otherwise>
            </choose>
        </set>
        where id = #{id}
          and audit_user_id = #{userId}
    </update>

    <update id="sendVerify">
        update sp_circuit_task
        set status        = #{status},
            audit_user_id = #{auditUserId}
        where id = #{id}
          and execution_user_id like '%' || #{userId} || '%'
        <!--    验证子任务全部完成    -->
        and (select count(item.id) > 0 from sp_circuit_task_item item where item.main_id = id and item.result is null) =
            false
    </update>

    <insert id="saveAll">
        INSERT INTO sp_circuit_task(id,
                                    type,
                                    code,
                                    name,
                                    task_type,
                                    execution_user_id,
                                    audit_user_id,
                                    start_time,
                                    end_time,
                                    real_start_time,
                                    real_end_time,
                                    station_id,
                                    template_id,
                                    status,
                                    creator,
                                    create_time,
                                    tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.type},
             generate_number_reset_different_day_with_date_prefix('sp_circuit_task' || #{element.tenantId}, 'fm000000', 999999),
             #{element.name},
             #{element.taskType},
             #{element.executionUserId},
             #{element.auditUserId},
             #{element.startTime},
             #{element.endTime},
             #{element.realStartTime},
             #{element.realEndTime},
             #{element.stationId},
             #{element.templateId},
             #{element.status},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update sp_circuit_task
        <set>
            type              = valueTable.type,
            code              = valueTable.code,
            name              = valueTable.name,
            task_type         = valueTable.taskType,
            execution_user_id = valueTable.executionUserId,
            audit_user_id     = valueTable.auditUserId,
            start_time        = valueTable.startTime,
            end_time          = valueTable.endTime,
            station_id        = valueTable.stationId,
            template_id       = valueTable.templateId
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.type},
             #{element.code},
             #{element.name},
             #{element.taskType},
             #{element.executionUserId},
             #{element.auditUserId},
             #{element.startTime},
             #{element.endTime},
             #{element.stationId},
             #{element.templateId})
        </foreach>
        ) as valueTable(id, type, code, name, taskType, executionUserId, auditUserId, startTime, endTime, stationId,
                        templateId)
        where id = valueTable.id
    </update>

    <update id="receive">
        update sp_circuit_task
        set status          = #{status},
            real_start_time = now()
        where id = #{id}
          and execution_user_id like '%' || #{userId} || '%'
    </update>


    <select id="totalOfUser" resultType="java.lang.Integer">
        select count(1)
        from sp_circuit_task
        where execution_user_id = #{userId};
    </select>

    <select id="totalStatusOfUser" resultType="java.lang.Integer">
        select count(1)
        from sp_circuit_task
        where status in
        <foreach collection="status" open="(" close=")" item="element" separator=",">
            #{element}
        </foreach>
        and execution_user_id = #{userId};
    </select>
</mapper>