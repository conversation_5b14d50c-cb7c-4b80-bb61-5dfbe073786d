<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.plan.PlanTaskDetailMapper">
    <sql id="Base_Column_List">
<!--@formatter:off-->
        <!--@sql select -->id,
                           main_id,
                           serial_id,
                           shelves_id,
                           normal_num,
                           exception_num,
                           device_rest_storage_count_by_serial_id_and_shelves_id(serial_id, shelves_id, tenant_id) count,
                           remark,
                           tenant_id<!--@sql from plan_task_detail -->
        <!--@formatter:on-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.plan.PlanTaskDetail">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="serial_id" property="serialId"/>
        <result column="count" property="count"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="normal_num" property="normalNum"/>
        <result column="exception_num" property="exceptionNum"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from plan_task_detail
        <where>
            <if test="serialId != null and serialId != ''">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="shelvesId != null and shelvesId != ''">
                and shelves_id = #{shelvesId}
            </if>
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update plan_task_detail
        <set>
            <if test="mainId != null">
                main_id = #{mainId},
            </if>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="shelvesId != null">
                shelves_id = #{shelvesId},
            </if>
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO plan_task_detail(id,
                                     main_id,
                                     serial_id,
                                     shelves_id,
                                     normal_num,
                                     exception_num,
                                     remark,
                                     tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.mainId},
             #{element.serialId},
             #{element.shelvesId},
             #{element.normalNum},
             #{element.exceptionNum},
             #{element.remark},
             #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update plan_task_detail
        <set>
            serial_id     = valueTable.serialId,
            shelves_id    = valueTable.shelvesId,
            normal_num    = valueTable.normalNum,
            exception_num = valueTable.exceptionNum,
            remark        = valueTable.remark
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.serialId},
             #{element.shelvesId},
             #{element.normalNum,jdbcType=DOUBLE},
             #{element.exceptionNum,jdbcType=DOUBLE},
             #{element.remark})
        </foreach>
        ) as valueTable(id, serialId, shelvesId, normalNum, exceptionNum, remark)
        where plan_task_detail.id = valueTable.id
    </update>

    <update id="complete">
        update plan_task_detail
        <set>
            <if test="normalNum != null">
                normal_num = #{normalNum},
            </if>
            <if test="exceptionNum != null">
                exception_num = #{exceptionNum},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteByMainId">
        delete
        from plan_task_detail
        where main_id = #{id}
    </delete>

    <update id="reset">
        update plan_task_detail
        set normal_num    = null,
            exception_num = null,
            remark        = null
        where id = #{id}
    </update>
</mapper>