# 事件总览功能说明

## 功能概述

事件总览是工单管理系统的核心功能之一，提供了对所有工单事件的统一查看和管理界面。该功能支持多维度筛选、地图定位、状态跟踪等特性。

## 主要功能

### 1. 事件查询与筛选
- **时间范围筛选**：支持按创建时间范围查询事件
- **来源筛选**：可按事件来源进行过滤
- **创建人筛选**：支持按创建人查询相关事件
- **关键词搜索**：支持在标题、地址、备注中搜索关键词

### 2. 事件列表展示
- **事件名称**：显示工单的标题信息
- **上报地点**：显示事件的上报地址
- **发生地点**：显示事件的实际发生地址，支持点击查看地图定位
- **事件状态**：使用不同颜色标签显示当前处理状态
- **创建时间**：显示事件的创建时间
- **创建人**：显示事件的创建者信息

### 3. 地图定位功能
- **地址点击定位**：点击发生地点可打开地图弹窗
- **高德地图集成**：使用高德地图显示精确位置
- **坐标信息显示**：显示经纬度坐标信息
- **位置标记**：在地图上标记事件发生位置
- **信息窗口**：点击标记显示详细地址信息

### 4. 操作功能
- **查看详情**：跳转到工单详情页面查看完整信息
- **地图定位**：快速定位到事件发生地点
- **分页浏览**：支持分页查看大量事件数据

## 技术实现

### 前端技术栈
- **Vue 3 + TypeScript**：现代化的前端框架
- **Element Plus**：UI组件库
- **高德地图API**：地图服务集成
- **Moment.js**：时间处理

### 后端技术栈
- **Spring Boot**：后端框架
- **MyBatis Plus**：数据库ORM
- **PostgreSQL**：数据库存储

### 核心组件

#### 1. EventOverview.vue
主要的事件总览页面组件，包含：
- 查询表单配置
- 数据表格展示
- 分页功能
- 操作按钮

#### 2. MapLocationDialog.vue
地图定位弹窗组件，提供：
- 地图初始化和显示
- 位置标记和信息窗口
- 加载状态处理
- 错误处理机制

#### 3. EventOverviewController.java
后端控制器，提供：
- 分页查询接口
- 事件详情查询
- 坐标信息更新
- 统计信息获取

#### 4. EventOverviewService.java
业务服务层，实现：
- 复杂查询逻辑
- 数据统计计算
- 坐标处理功能
- 地理编码服务

## API接口

### 1. 获取事件总览分页数据
```
GET /api/eventOverview
```

**参数：**
- `page`: 页码
- `size`: 每页大小
- `fromTime`: 开始时间（时间戳）
- `toTime`: 结束时间（时间戳）
- `source`: 来源
- `organizerId`: 创建人ID
- `status`: 状态
- `title`: 标题（模糊查询）
- `address`: 地址（模糊查询）

### 2. 获取事件详情
```
GET /api/eventOverview/{id}
```

### 3. 更新事件坐标
```
PUT /api/eventOverview/{id}/coordinate
```

### 4. 获取事件统计信息
```
GET /api/eventOverview/statistics
```

## 数据库设计

### 主要字段
- `coordinate`: 地理位置坐标（格式：经度,纬度）
- `coordinate_name`: 地理位置名称
- `address`: 事件发生地址
- `upload_address`: 上报地址

### 索引优化
- 创建时间索引
- 状态索引
- 租户ID索引
- 坐标索引
- 复合索引（租户+时间+状态）

## 使用说明

### 1. 基本查询
1. 打开事件总览页面
2. 设置查询条件（时间范围、来源等）
3. 点击"查询"按钮获取结果

### 2. 地图定位
1. 在事件列表中找到目标事件
2. 点击"地图定位"按钮
3. 在弹出的地图窗口中查看位置
4. 可点击标记查看详细信息

### 3. 查看详情
1. 点击事件列表中的"查看"按钮
2. 系统将打开新窗口显示工单详情

## 注意事项

1. **坐标数据**：只有设置了坐标信息的事件才能进行地图定位
2. **权限控制**：用户只能查看自己租户下的事件数据
3. **性能优化**：大数据量查询时建议设置合适的时间范围
4. **地图服务**：需要确保高德地图API密钥配置正确

## 扩展功能

### 1. 批量操作
- 批量更新坐标信息
- 批量状态变更
- 批量导出数据

### 2. 统计分析
- 事件热点地区分析
- 处理效率统计
- 趋势分析图表

### 3. 地理编码
- 根据地址自动获取坐标
- 地址标准化处理
- 位置验证功能

## 故障排除

### 常见问题
1. **地图无法加载**：检查网络连接和API密钥
2. **坐标显示错误**：验证坐标格式是否正确
3. **查询结果为空**：检查查询条件和数据权限

### 调试方法
1. 查看浏览器控制台错误信息
2. 检查后端日志
3. 验证数据库连接和数据完整性
