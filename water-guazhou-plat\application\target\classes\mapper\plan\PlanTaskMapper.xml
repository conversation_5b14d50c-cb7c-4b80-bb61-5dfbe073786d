<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.plan.PlanTaskMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           "name",
                           execution_type,
                           storehouse_id,
                           (select name from store where id = storehouse_id) storehouse_name,
                           execution_user_id,
                           start_time,
                           end_time,
                           real_start_time,
                           real_end_time,
                           creator,
                           create_time,
                           "type",
                           "status",
                           "result",
                           tenant_id<!--@sql from plan_task -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.plan.PlanTask">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="execution_type" property="executionType"/>
        <result column="storehouse_id" property="storehouseId"/>
        <result column="storehouse_name" property="storehouseName"/>
        <result column="execution_user_id" property="executionUserId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="real_start_time" property="realStartTime"/>
        <result column="real_end_time" property="realEndTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="result" property="result"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from plan_task
        <where>
            <if test="keyWord != null and keyWord != ''">
                and "name" like '%' || #{keyWord} || '%'
            </if>
            <if test="storehouseId != null and storehouseId != ''">
                and storehouse_id = #{storehouseId}
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                and execution_user_id = #{executionUserId}
            </if>
            <if test="executionDepartmentId != null and executionDepartmentId != ''">
                and is_user_at_department(execution_user_id, #{executionDepartmentId})
            </if>
            <if test="isOverdue != null">
                <choose>
                    <when test="isOverdue">
                        and real_end_time is null
                        and now() > plan_task.end_time
                    </when>
                    <otherwise>
                        and now() &lt;= plan_task.end_time
                    </otherwise>
                </choose>
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="startTime != null">
                and onday(start_time, #{startTime})
            </if>
            <if test="endTime != null">
                and onday(end_time, #{endTime})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update plan_task
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="executionType != null">
                execution_type = #{executionType},
            </if>
            <if test="executionUserId != null and executionUserId != ''">
                execution_user_id = #{executionUserId},
            </if>
            <if test="storehouseId != null">
                storehouse_id = #{storehouseId},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="complete">
        update plan_task
        <set>
            status        = #{status},
            real_end_time = now(),
            <if test="req.result != null">
                result = #{req.result},
            </if>
        </set>
        where id = #{req.id}
          --and plan_task_can_complete(plan_task.id)
          and execution_user_id = #{req.currentUser}
    </update>

    <update id="resetChildren">
        update plan_task_detail
        set normal_num    = null,
            exception_num = null,
            remark        = null
        where main_id = #{id}
    </update>

    <update id="reset">
        update plan_task
        set status = 'PENDING'
        where id = #{id}
    </update>

    <select id="getIsRight" resultType="java.lang.Boolean">
        select plan_task_is_right(#{id});
    </select>

    <update id="start">
        update plan_task
        <set>
            status          = #{status},
            real_start_time = now(),
        </set>
        where id = #{req.id}
          and plan_task_can_startup(plan_task.id)
          and execution_user_id = #{req.currentUser}
    </update>

    <select id="canOperate" resultType="boolean">
        select execution_user_id = #{currentUser}
        from plan_task
        where id = #{id}
    </select>

    <insert id="save">
        INSERT INTO plan_task(id,
                              code,
                              name,
                              execution_type,
                              storehouse_id,
                              execution_user_id,
                              start_time,
                              end_time,
                              real_start_time,
                              real_end_time,
                              creator,
                              create_time,
                              type,
                              status,
                              result,
                              remark,
                              tenant_id)
        VALUES (#{id},
                generate_number_reset_different_day_with_date_prefix('plan_task' || #{tenantId},'fm000000', 999999),
                #{name},
                #{executionType},
                #{storehouseId},
                #{executionUserId},
                #{startTime},
                #{endTime},
                #{realStartTime},
                #{realEndTime},
                #{creator},
                #{createTime},
                #{type},
                #{status},
                #{result},
                #{remark},
                #{tenantId})
    </insert>
</mapper>