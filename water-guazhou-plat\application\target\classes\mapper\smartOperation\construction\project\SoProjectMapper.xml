<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoProjectMapper">
    <sql id="Base_Column_List">
        <bind name="projectScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_PROJECT"/>
        <!--@sql select -->
        id,
        code,
        name,
        type_id,
        (select type.name from so_general_type type where id = type_id) type_name,
        scale,
        organization,
        principal,
        phone,
        address,
        remark,
        estimate,
        start_time,
        expect_end_time,
        attachments,
        create_time,
        creator,
        update_time,
        update_user,
        tenant_id,
        (
        select
        <!--无关联工程-->
        (select count(1) = 0
         from so_project project
                  join so_construction cons
                       on project.code = project_code and project.tenant_id = cons.tenant_id
         where project.id = so_project.id)
            and
        <!--无关联设备项-->
        (select count(1) = 0
         from so_device_item item
         where item.scope = #{projectScope}
           and item.identifier = so_project.code
           and so_project.tenant_id = item.tenant_id)
        )can_be_delete
        <!--@sql from so_project -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoProject">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type_id" property="typeId"/>
        <result column="type_name" property="typeName"/>
        <result column="scale" property="scale"/>
        <result column="organization" property="organization"/>
        <result column="principal" property="principal"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="remark" property="remark"/>
        <result column="estimate" property="estimate"/>
        <result column="start_time" property="startTime"/>
        <result column="expect_end_time" property="expectEndTime"/>
        <result column="attachments" property="attachments"/>
        <result column="create_time" property="createTime"/>
        <result column="creator" property="creator"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="can_be_delete" property="canBeDelete"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_project
        <where>
            <if test="code != null">
                and code like '%'|| #{code} ||'%'
            </if>
            <if test="name != null">
                and "name" like '%'|| #{name} ||'%'
            </if>
            <if test="typeId != null">
                and type_id = #{typeId}
            </if>
            <if test="startTimeFrom != null">
                and start_time >= #{startTimeFrom}
            </if>
            <if test="startTimeTo != null">
                and start_time &lt;= #{startTimeTo}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update so_project
        <set>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="typeId != null and typeId != ''">
                type_id = #{typeId},
            </if>
            <if test="scale != null and scale != ''">
                scale = #{scale},
            </if>
            <if test="organization != null and organization != ''">
                organization = #{organization},
            </if>
            <if test="principal != null and principal != ''">
                principal = #{principal},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="estimate != null">
                estimate = #{estimate},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="expectEndTime != null">
                expect_end_time = #{expectEndTime},
            </if>
            <if test="attachments != null and attachments != ''">
                attachments = #{attachments},
            </if>
            update_time = #{updateTime},
            update_user = #{updateUser},
            tenant_id   = #{tenantId},
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_project
        <set>
            code            = #{code},
            name            = #{name},
            type_id         = #{typeId},
            scale           = #{scale},
            organization    = #{organization},
            principal       = #{principal},
            phone           = #{phone},
            address         = #{address},
            remark          = #{remark},
            estimate        = #{estimate},
            start_time      = #{startTime},
            expect_end_time = #{expectEndTime},
            attachments     = #{attachments},
            update_time     = #{updateTime},
            update_user     = #{updateUser}
        </set>
        where id = #{id}
    </update>

    <select id="generateCode" resultType="java.lang.String">
        <!--@formatter:off-->
        select 'Q' || generate_number_reset_different_day_with_simple_date_prefix('so_project' || #{tenantId}, 'fm000000', 999999)
        <!--@formatter:on-->
    </select>

    <delete id="remove">
        delete
        from so_project project
        where id = #{id}
          and (select count(1) = 0
               from so_project_archive
               where project_code = project.code
                 and tenant_id = project.tenant_id)
          and (select count(1) = 0
               from so_construction_archive
               where construction_code in (select code from so_construction where project_code = project.code)
                 and tenant_id = project.tenant_id)
    </delete>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_project outside
        where code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_project where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_project where id = #{id})
        </if>
    </select>

    <select id="getCodeById" resultType="java.lang.String">
        select code
        from so_project
        where id = #{id}
    </select>

    <select id="getByConstructionCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_project
        where code = (select project_code from so_construction where code = #{constructionCode})
          and tenant_id = #{tenantId}
    </select>

    <select id="canBeDelete" resultType="boolean">
        <bind name="projectScope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_PROJECT"/>
        select
        <!--无关联工程-->
        (select count(1) = 0
         from so_project project
                  join so_construction cons
                       on project.code = project_code and project.tenant_id = cons.tenant_id
         where project.id = #{id})
            and
        <!--无关联设备项-->
        (select count(1) = 0
         from so_project project
                  join so_device_item item
                            on item.identifier = project.code and item.scope = #{projectScope} and
                               project.tenant_id = item.tenant_id
         where project.id = #{id})
    </select>
</mapper>