<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.stationSetting.StationArchivesMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.stationSetting.StationArchives">
        SELECT
            a.*
        FROM
            tb_station_archives a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND (a.name LIKE '%' || #{param.keyword} || '%'
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.stationId != null and param.stationId != ''">
                AND a.station_id = #{param.stationId}
            </if>
            <if test="param.categoryId != null and param.categoryId != ''">
                AND a.category_id = #{param.categoryId}
            </if>

        </where>
        ORDER BY a.create_time
    </select>

</mapper>