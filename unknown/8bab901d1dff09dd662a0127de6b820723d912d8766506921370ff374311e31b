<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.StoreMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           "name",
                           address,
                           order_num,
                           manager_id,
                           remark,
                           tel,
                           creator,
                           create_time,
                           update_time,
                           tenant_id<!--@sql from store -->
    </sql>
    <sql id="Parent_Column_Query">
        <!--@sql select -->id,
                           code,
                           "name",
                           address,
                           order_num,
                           manager_id,
                           remark,
                           tel,
                           creator,
                           create_time,
                           update_time,
                           tenant_id,
                           1 as layer
        <!--@sql from store -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.Store">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="address" property="address"/>
        <result column="order_num" property="orderNum"/>
        <result column="manager_id" property="managerId"/>
        <result column="remark" property="remark"/>
        <result column="tel" property="tel"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from store
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="managerId != null and managerId != ''">
                and manager_id = #{managerId}
            </if>
            <if test="(managerId == null or managerId == '') and managerDepartmentId != null and managerDepartmentId != ''">
                and is_user_at_department(store.manager_id, #{managerDepartmentId})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
            order by order_num
        </where>
    </select>

    <update id="update">
        update store
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="managerId != null">
                manager_id = #{managerId},
            </if>
            <if test="managerId != null">
                tel = #{tel},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from store
        where id = #{id}
    </select>

    <select id="getCode" resultType="java.lang.String">
        select code
        from store
        where id = #{id}
    </select>

    <select id="canBeDelete" resultType="boolean">
        select count(1) = 0 from goods_shelf where parent_id = #{id}
    </select>
</mapper>