<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.DeviceSettleJournalMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           device_label_code,
                           install_user_id,
                           project_id,
                           install_time,
                           install_address_id,
                           address,
                           remark,
                           create_time,
                           tenant_id<!--@sql from device_settle_journal -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.DeviceSettleJournal">
        <result column="id" property="id"/>
        <result column="device_label_code" property="deviceLabelCode"/>
        <result column="install_user_id" property="installUserId"/>
        <result column="project_id" property="projectId"/>
        <result column="install_time" property="installTime"/>
        <result column="install_address_id" property="installAddressId"/>
        <result column="address" property="address"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_settle_journal
        <where>
            <if test="deviceLabelCode != null and deviceLabelCode != ''">
                and device_label_code like '%' || #{deviceLabelCode} || '%'
            </if>
            <if test="installUserId != null and installUserId != ''">
                and install_user_id = #{installUserId}
            </if>
            <if test="projectId != null and projectId != ''">
                and project_id = #{projectId}
            </if>
            <if test="installTime != null">
                and onday(install_time, #{installTime})
            </if>
            <if test="installAddressId != null and installAddressId != ''">
                and install_address_id = #{installAddressId}
            </if>
            <if test="address != null and address != ''">
                and address like '%' || #{address} || '%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update device_settle_journal
        <set>
            <if test="deviceLabelCode != null">
                device_label_code = #{deviceLabelCode},
            </if>
            <if test="installUserId != null">
                install_user_id = #{installUserId},
            </if>
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="installTime != null">
                install_time = #{installTime},
            </if>
            <if test="installAddressId != null">
                install_address_id = #{installAddressId},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findSettleNames" resultType="java.lang.String">
        select distinct address from device_settle_journal where tenant_id = #{tenantId}
    </select>
</mapper>