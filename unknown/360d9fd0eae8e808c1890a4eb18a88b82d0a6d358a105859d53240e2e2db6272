<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.alarmV2.AlarmRuleMapper">
    <insert id="batchInsert">
        INSERT INTO tb_alarm_rule(id, alarm_level, alarm_type, create_time, process_method, re_alarm_type, re_alarm_unit, re_alarm_value, remark, remote_station_attr_id, remote_video_id, rule_param, rule_type, send_way, station_attr_id, station_id, status, tenant_id, title, type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.alarmLevel},
            #{item.alarmType},
            #{item.createTime},
            #{item.processMethod},
            #{item.reAlarmType},
            #{item.reAlarmUnit},
            #{item.reAlarmValue},
            #{item.remark},
            #{item.remoteStationAttrId},
            #{item.remoteVideoId},
            #{item.ruleParam},
            #{item.ruleType},
            #{item.sendWay},
            #{item.stationAttrId},
            #{item.stationId},
            #{item.status},
            #{item.tenantId},
            #{item.title},
            #{item.type})
        </foreach>
    </insert>

    <select id="findList" resultType="org.thingsboard.server.dao.model.DTO.AlarmRuleDTO">
        SELECT
        a.*, b.name AS "stationAttrName", c.name AS "stationName"
        FROM
        tb_alarm_rule a LEFT JOIN tb_station_attr b ON a.station_attr_id = b.id LEFT JOIN tb_station c ON c.id = b.station_id
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.name != null and param.name != ''">
                AND b.name LIKE '%' || #{param.name} ||'%'
            </if>
            <if test="param.alarmType != null and param.alarmType != ''">
                AND a.alarm_type = #{param.alarmType}
            </if>
            <if test="param.alarmLevel != null and param.alarmLevel != ''">
                AND a.alarm_level = #{param.alarmLevel}
            </if>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>