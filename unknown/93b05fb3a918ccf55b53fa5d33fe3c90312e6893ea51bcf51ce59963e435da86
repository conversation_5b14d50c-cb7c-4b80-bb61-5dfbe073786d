<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayReportItemMapper">

    <insert id="batchInsert">
        INSERT INTO tb_assay_report_item(id, report_id, item_id, tenant_id, create_time) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.reportId},
            #{element.itemId},
            #{element.tenantId},
            #{element.createTime}
            )
        </foreach>
    </insert>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.assay.AssayReportItem">
        select a.*, b.title, b.target, b.unit
        from tb_assay_report_item a
        left join tb_assay_item b on a.item_id = b.id
        where a.report_id = #{reportId}
    </select>
</mapper>