<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardEventRecordMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        record_id,
        user_id,
        content,
        create_time,
        tenant_id
        <!--@sql from guard_event_record -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardEventRecord">
        <result column="id" property="id"/>
        <result column="record_id" property="recordId"/>
        <result column="user_id" property="userId"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_event_record
        <where>
            <if test="recordId != null and recordId != ''">
                and record_id = #{recordId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <delete id="deleteByRecordId">
        delete
        from guard_event_record
        where record_id = #{recordId}
    </delete>
</mapper>