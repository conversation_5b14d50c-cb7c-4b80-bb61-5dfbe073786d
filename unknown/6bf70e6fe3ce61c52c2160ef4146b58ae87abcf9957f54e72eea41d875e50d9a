<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           parent_id,
                           remark,
                           tenant_id<!--@sql from sm_circuit_district -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.district.SMCircuitDistrict">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="getTopRoot" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district
        where parent_id is null;
    </select>

    <select id="findActualRoots" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district
        where parent_id = sm_circuit_district_get_top_root_id()
          and tenant_id = #{tenantId};
    </select>

    <select id="findChildren" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_district
        where parent_id = #{parentId}
          and tenant_id = #{tenantId};
    </select>

    <update id="update">
        update sm_circuit_district
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
          and id != sm_circuit_district_get_top_root_id()
    </update>


    <delete id="delete">
        delete
        from sm_circuit_district
        where id = #{id}
          and id != sm_circuit_district_get_top_root_id()
    </delete>

    <update id="updateFully">
        update sm_circuit_district
        <set>
            name   = #{name},
            remark = #{remark},
        </set>
        where id = #{id}
          and id != sm_circuit_district_get_top_root_id()
    </update>

    <select id="getNameById" resultType="java.lang.String">
        select sm_circuit_district_get_name(#{id})
    </select>

    <select id="getPoints" resultType="java.lang.String">
        select points from sm_circuit_district_area
    </select>
</mapper>