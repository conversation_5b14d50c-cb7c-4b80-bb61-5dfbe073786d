<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallIVRLogMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallIVRLog">
        select a.*, b.phone, c.name as eventName, c.audio_url, c.audio_name
        from tb_service_call_ivr_log a
            left join tb_service_call_log b on a.call_id = b.id
            left join tb_service_call_ivr_event c on a.event_Id = c.id
        where b.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_ivr_log a left join tb_service_call_log b on a.call_id = b.id
        where b.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
    </select>
    <select id="getAllByTime" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallIVRLog">
        select a.*, b.phone, c.name as eventName, c.audio_url, c.audio_name, b.call_time, b.ring_time, b.listen_time, b.end_time, b.evaluate
        from tb_service_call_ivr_log a
        left join tb_service_call_log b on a.call_id = b.id
        left join tb_service_call_ivr_event c on a.event_Id = c.id
        where 1 = 1
        <if test="startTime != null">
            and a.create_time &gt;= to_timestamp(#{startTime})
        </if>
        <if test="endTime != null">
            and a.create_time &lt;= to_timestamp(#{endTime})
        </if>
        order by a.create_time asc
    </select>
    <select id="getCallAndIvrLog" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallIVRLog">
        select distinct a.*, b.phone, c.name as eventName, c.audio_url, c.audio_name, b.call_time, b.ring_time, b.listen_time, b.end_time, b.evaluate, b.file_url, b.real_end_time
        from tb_service_call_log b
        left join tb_service_call_ivr_log a on a.call_id = b.id
        left join tb_service_call_ivr_event c on a.event_Id = c.id
        where 1 = 1 and b.direction = '0' and call_id is not null
        <if test="startTime != null">
            and b.call_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and b.call_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        order by b.call_time asc
    </select>

</mapper>