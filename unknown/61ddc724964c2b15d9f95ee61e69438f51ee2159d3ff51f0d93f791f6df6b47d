<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportTableFooterMapper">
    <insert id="batchInsert">
        insert into tb_report_table_footer(id, pid, title, order_num, create_time, tenant_id) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
                #{element.id},
                #{element.pid},
                #{element.title},
                #{element.orderNum},
                #{element.createTime},
                #{element.tenantId}
            )
        </foreach>
    </insert>
</mapper>