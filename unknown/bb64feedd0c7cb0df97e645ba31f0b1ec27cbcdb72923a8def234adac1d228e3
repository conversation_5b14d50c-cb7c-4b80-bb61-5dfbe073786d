<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.call.CallLogCMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.call.CallLogC">
        select a.*, c.first_name as processPersonName, b.call_time, b.phone, b.file_url, b.file_name
        from tb_service_call_log_c a left join tb_service_call_log b on a.main_id = b.id
        left join tb_user c on a.process_person_id = c.id
        where b.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and b.call_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and b.call_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        <if test="status != null and status != ''">
            and a.status= #{status}
        </if>
        order by b.call_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_call_log_c a left join tb_service_call_log b on a.main_id = b.id
        left join tb_user c on a.process_person_id = c.id
        where b.phone like '%'||#{phone}||'%'
        <if test="startTime != null">
            and b.call_time &gt;= to_timestamp(#{startTime} / 1000)
        </if>
        <if test="endTime != null">
            and b.call_time &lt;= to_timestamp(#{endTime} / 1000)
        </if>
        <if test="status != null and status != ''">
            and a.status= #{status}
        </if>
    </select>

</mapper>