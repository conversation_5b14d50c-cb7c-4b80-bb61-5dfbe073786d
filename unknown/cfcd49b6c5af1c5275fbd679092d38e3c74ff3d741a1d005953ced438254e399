<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.WorkOrderReminderMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderReminder">
        select *
        from work_order_reminder
        <where>
            <if test="param.content != null">
                and content like '%'||#{param.content}||'%'
            </if>
            <if test="param.isDel != null">
                and is_del = #{param.isDel}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>