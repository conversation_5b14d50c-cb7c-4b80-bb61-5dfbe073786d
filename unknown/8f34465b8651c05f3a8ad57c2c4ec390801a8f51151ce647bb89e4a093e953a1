<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.GanInstallMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.GanInstall">
        select * from tb_gan_install
        <where>
            <if test="param.userId != null and param.userId != ''">
                and user_id = #{param.userId}
            </if>
            <if test="param.status != null and param.status != ''">
                and status in
                <foreach collection="param.status.split(',')" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="param.name != null and param.name != ''">
                 and (name like '%' || #{param.name} || '%' or install_name like '%' || #{param.name} || '%' or install_contacts like '%' || #{param.name} || '%' )
            </if>
            <if test="param.code != null and param.code != ''">
                 and code like '%' || #{param.code} || '%'
            </if>
            <if test="param.idCard != null and param.idCard != ''">
                 and (id_card like '%' || #{param.idCard} || '%' or install_id_card like '%' || #{param.idCard} || '%' )
            </if>
            <if test="param.phone != null and param.phone != ''">
                 and (phone like '%' || #{param.phone} || '%' or install_phone like '%' || #{param.phone} || '%')
            </if>
            <if test="param.address != null and param.address != ''">
                 and (address like '%' || #{param.address} || '%' or install_address like '%' || #{param.address} || '%' )
            </if>
            <if test="param.type != null and param.type != ''">
                and type = #{param.type}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>