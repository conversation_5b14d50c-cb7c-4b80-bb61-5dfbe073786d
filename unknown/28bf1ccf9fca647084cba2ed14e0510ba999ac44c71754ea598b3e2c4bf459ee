<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportDatabaseMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.report.ReportDatabase">
        select id, name, type, host, port, server, username, remark, create_time, tenant_id
               from tb_report_database a
        where a.name like '%'||#{name}||'%' and a.tenant_id = #{tenantId}
        order by a.create_time desc
    </select>
</mapper>