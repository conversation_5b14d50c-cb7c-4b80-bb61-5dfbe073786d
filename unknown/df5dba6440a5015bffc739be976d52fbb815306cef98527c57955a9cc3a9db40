<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipePartitionCustMapper">
    <insert id="batchSave">
        insert into tb_pipe_partition_cust(id, partition_id, cust_code, business_hall, copy_meter_user, create_time, tenant_id) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
             #{item.id}, #{item.partitionId}, #{item.custCode}, #{item.businessHall}, #{item.copyMeterUser}, #{item.createTime}, #{item.tenantId}
            )
        </foreach>
    </insert>
    <update id="batchRemove">
        update tb_pipe_partition_cust set partition_id = null where id in
        <foreach collection="idList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdatePartitionId">
        <foreach collection="pipePartitionCustList" item="item">
            update tb_pipe_partition_cust set partition_id = #{item.partitionId} where id = #{item.id};
        </foreach>
    </update>

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipePartitionCust">
        select a.*, a.cust_code as custCode, b.name as custName, b.phone, waterType.name as WaterCategory,
               book.code as meterBookCode, book.name as meterBookName, b.address, tpp.name as partitionName,
               to_char(a.create_time, 'yyyy-mm-dd HH24:MM:SS') as createTimeStr
        from tb_pipe_partition_cust a
        left join revenue.tb_cust_info b on a.cust_code = b.code and a.tenant_id = b.tenant_id
        left join revenue.tb_meter_book book on b.meter_book_id = book.id
        left join revenue.tb_sys_code_detail waterType on b.water_category = waterType.key and waterType.code = 'WaterCategoryType'
        left join tb_pipe_partition tpp on a.partition_id = tpp.id
        where a.tenant_id = #{param.tenantId}
        <if test="param.custCode != null and param.custCode != ''">
            and a.cust_code like '%' || #{param.custCode} || '%'
        </if>
        <if test="param.custName != null and param.custName != ''">
            and b.name like '%' || #{param.custName} || '%'
        </if>
        <if test="param.waterCategory != null and param.waterCategory != ''">
            and b.water_category = #{param.waterCategory}
        </if>
        <if test="param.meterBookId != null and param.meterBookId != ''">
            and b.meter_book_id = #{param.meterBookId}
        </if>
        <if test="param.copyMeterUser != null and param.copyMeterUser != ''">
            and a.copy_meter_user like '%' || #{param.copyMeterUser} || '%'
        </if>
        <if test="param.businessHall != null and param.businessHall != ''">
            and a.business_hall like '%' || #{param.businessHall} || '%'
        </if>
        <if test="param.partitionId != null and param.partitionId != ''">
            and a.partition_id = #{param.partitionId}
        </if>
        <if test="param.isMount != null and param.isMount != ''">
            <choose>
                <when test="param.isMount == '1'.toString()">
                    and a.partition_id is not null
                </when>
                <otherwise>
                    and a.partition_id is null
                </otherwise>
            </choose>
        </if>
        <if test="param.address != null and param.address != ''">
            and b.address like '%' || #{param.address} || '%'
        </if>
    </select>

</mapper>