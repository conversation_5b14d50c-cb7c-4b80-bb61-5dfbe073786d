<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.ProcessStepAttachmentMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.ProcessStepAttachment">
        select a.*, b.name as stepName, c.name as mainName
        from tb_install_process_step_attachment a
            left join tb_install_process_step b on a.step_id = b.id
            left join tb_install_process_type c on a.main_id = c.id
        where a.name like '%' || #{name} || '%'
            and a.tenant_id = #{tenantId}
            and c.id like '%' || #{mainId} || '%'
            and b.id like '%' || #{stepId} || '%'
        order by a.code desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_install_process_step_attachment a
             left join tb_install_process_step b on a.step_id = b.id
             left join tb_install_process_type c on a.main_id = c.id
        where a.name like '%' || #{name} || '%'
            and a.tenant_id = #{tenantId}
            and c.id like '%' || #{mainId} || '%'
            and b.id like '%' || #{stepId} || '%'
    </select>

</mapper>