<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.ConstructionProjectMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           "name",
                           address,
                           org_id,
                           construction_side,
                           insert_time,
                           start_time,
                           end_time,
                           creator,
                           create_time,
                           update_time,
                           tenant_id<!--@sql from construction_project -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.store.ConstructionProject">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="address" property="address"/>
        <result column="org_id" property="orgId"/>
        <result column="construction_side" property="constructionSide"/>
        <result column="insert_time" property="insertTime"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from construction_project
        <where>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="address != null and address != ''">
                and address like '%' || #{address} || '%'
            </if>
            <if test="orgId != null and orgId != ''">
                and org_id = #{orgId}
            </if>
            <if test="constructionSide != null and constructionSide != ''">
                and construction_side like '%' || #{constructionSide} || '%'
            </if>
            <if test="constructionSide != null and constructionSide != ''">
                and construction_side like '%' || #{constructionSide} || '%'
            </if>
            <if test="createTime != null">
                and onday(create_time, #{createTime})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <update id="update">
        update construction_project
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="orgId != null">
                org_id = #{orgId},
            </if>
            <if test="constructionSide != null">
                construction_side = #{constructionSide},
            </if>
            <if test="insertTime != null">
                insert_time = #{insertTime},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getNameById" resultType="java.lang.String">
        select name
        from construction_project
        where id = #{id}
    </select>
</mapper>