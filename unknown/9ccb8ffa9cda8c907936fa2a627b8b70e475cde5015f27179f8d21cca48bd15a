<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.DispatchMethodMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->a.id,
                           a.code,
                           a.name,
                           a.type,
                           a.date_label,
                           a.station_id,
                           a.time,
                           a.remark,
                           a.weather_type,
                           a.max_temperature,
                           a.min_temperature,
                           a.rainfall,
                           a.relative_humidity,
                           a.water_supply,
                           a.power_consumption,
                           a.water_level,
                           a.creator,
                           a.create_time,
                           a.edit_user,
                           a.edit_time,
                           a.is_enabled,
                           a.tenant_id<!--@sql from sp_dispatch_method -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.DispatchMethod">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="date_label" property="dateLabel"/>
        <result column="station_id" property="stationId"/>
        <result column="time" property="time"/>
        <result column="remark" property="remark"/>
        <result column="weather_type" property="weatherType"/>
        <result column="max_temperature" property="maxTemperature"/>
        <result column="min_temperature" property="minTemperature"/>
        <result column="rainfall" property="rainfall"/>
        <result column="relative_humidity" property="relativeHumidity"/>
        <result column="water_supply" property="waterSupply"/>
        <result column="power_consumption" property="powerConsumption"/>
        <result column="water_level" property="waterLevel"/>
        <result column="edit_user" property="editUser"/>
        <result column="edit_time" property="editTime"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>, b.name AS "stationName"
        from sp_dispatch_method a LEFT JOIN tb_station b ON a.station_id = b.id
        <where>
            <if test="name != null and name != ''">
                and a."name" like '%'|| #{name} ||'%'
            </if>
            <if test="type != null and type != ''">
                and a."type" = #{type}
            </if>
            <if test="dateLabel != null and dateLabel != ''">
                and a.date_label = #{dateLabel}
            </if>
            <if test="timeFrom != null">
                and a.time >= #{timeFrom}
            </if>
            <if test="timeTo != null">
                and a.time &lt;= #{timeTo}
            </if>
            <if test="weatherType != null and weatherType != ''">
                and a.weather_type = #{weatherType}
            </if>
            <if test="waterSupplyFrom != null">
                and a.water_supply >= #{waterSupplyFrom}
            </if>
            <if test="waterSupplyTo != null">
                and a.water_supply &lt;= #{waterSupplyTo}
            </if>
            <if test="powerConsumptionFrom != null">
                and a.power_consumption >= #{powerConsumptionFrom}
            </if>
            <if test="powerConsumptionTo != null">
                and a.power_consumption &lt;= #{powerConsumptionTo}
            </if>
            <if test="fromTime != null">
                and a.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and a.create_time &lt;= #{toTime}
            </if>
            and a.tenant_id = #{tenantId}
        </where>
        order by a.create_time desc
    </select>

    <update id="update">
        update sp_dispatch_method
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="dateLabel != null">
                date_label = #{dateLabel},
            </if>
            <if test="stationId != null">
                station_id = #{stationId},
            </if>
            <if test="time != null">
                time = #{time},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="weatherType != null">
                weather_type = #{weatherType},
            </if>
            <if test="maxTemperature != null">
                max_temperature = #{maxTemperature},
            </if>
            <if test="minTemperature != null">
                min_temperature = #{minTemperature},
            </if>
            <if test="rainfall != null">
                rainfall = #{rainfall},
            </if>
            <if test="relativeHumidity != null">
                relative_humidity = #{relativeHumidity},
            </if>
            <if test="waterSupply != null">
                water_supply = #{waterSupply},
            </if>
            <if test="powerConsumption != null">
                power_consumption = #{powerConsumption},
            </if>
            <if test="waterLevel != null">
                water_level = #{waterLevel},
            </if>
            edit_user = #{editUser},
            edit_time = now()
        </set>
        where id = #{id}
    </update>

    <update id="switchEnabled">
        update sp_dispatch_method
        set is_enabled = #{enabled}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO sp_dispatch_method(id,
                                       code,
                                       name,
                                       type,
                                       date_label,
                                       station_id,
                                       time,
                                       remark,
                                       weather_type,
                                       max_temperature,
                                       min_temperature,
                                       rainfall,
                                       relative_humidity,
                                       water_supply,
                                       power_consumption,
                                       water_level,
                                       edit_user,
                                       edit_time,
                                       is_enabled,
                                       creator,
                                       create_time,
                                       tenant_id)
        VALUES (#{id},
                generate_number_reset_different_day_with_date_prefix('sp_dispatch_method', 'fm000000', 999999),
                #{name},
                #{type},
                #{dateLabel},
                #{stationId},
                #{time},
                #{remark},
                #{weatherType},
                #{maxTemperature},
                #{minTemperature},
                #{rainfall},
                #{relativeHumidity},
                #{waterSupply},
                #{powerConsumption},
                #{waterLevel},
                #{editUser},
                #{editTime},
                #{isEnabled},
                #{creator},
                #{createTime},
                #{tenantId})
    </insert>

    <update id="updateFully">
        update sp_dispatch_method
        set name              = #{name},
            type              = #{type},
            date_label        = #{dateLabel},
            station_id        = #{stationId},
            time              = #{time},
            remark            = #{remark},
            weather_type      = #{weatherType},
            max_temperature   = #{maxTemperature},
            min_temperature   = #{minTemperature},
            rainfall          = #{rainfall},
            relative_humidity = #{relativeHumidity},
            water_supply      = #{waterSupply},
            power_consumption = #{powerConsumption},
            water_level       = #{waterLevel},
            edit_user         = #{editUser},
            edit_time         = now()
        where id = #{id}
    </update>
</mapper>