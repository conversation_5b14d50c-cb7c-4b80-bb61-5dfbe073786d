<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.gis.GisProjectMapper">
    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.gis.GisProject">
        SELECT
            a.*
        FROM
            tb_gis_project a
        <where>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.name != null and param.name != ''">
                AND a.name LIKE '%' || #{param.name} || '%'
            </if>
            <if test="param.beginTime != null">
                AND a.complete_date <![CDATA[ >= ]]> #{param.beginTime}
            </if>
            <if test="param.endTime != null">
                AND a.complete_date <![CDATA[ <= ]]> #{param.endTime}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

</mapper>