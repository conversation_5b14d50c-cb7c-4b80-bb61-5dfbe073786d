<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalPolicyContentMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        title,
        content,
        create_time,
        type_id,
        (select name from ss_portal_policy where id = type_id) type_name,
        tenant_id
        <!--@sql from ss_portal_policy_content -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalPolicyContent">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="type_id" property="typeId"/>
        <result column="type_name" property="typeName"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_policy_content
        <where>
            <if test="title != null and title != ''">
                and title like '%' || #{title} || '%'
            </if>
            <if test="typeId != null and typeId != ''">
                and type_id = #{typeId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update ss_portal_policy_content
        set title        = #{title},
            content        = #{content},
            type_id     = #{typeId}
        where id = #{id}
    </update>
</mapper>