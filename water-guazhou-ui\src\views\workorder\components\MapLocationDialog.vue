<!-- 地图定位弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="事件地址定位"
    width="900px"
    :before-close="handleClose"
    class="map-location-dialog"
  >
    <div class="map-location-container">
      <div class="address-info">
        <div class="info-item">
          <span class="label">地址：</span>
          <span class="value">{{ address || '暂无地址信息' }}</span>
        </div>
        <div v-if="coordinates" class="info-item">
          <span class="label">坐标：</span>
          <span class="value">{{ coordinates }}</span>
        </div>
      </div>
      <div class="map-container">
        <div v-if="!coordinates" class="no-location">
          <el-icon class="location-icon"><LocationInformation /></el-icon>
          <p class="no-location-text">暂无位置信息</p>
          <p class="no-location-desc">该事件未设置地理坐标，无法在地图上显示位置</p>
        </div>
        <div v-else id="mapContainer" class="map-view">
          <div class="map-loading" v-if="mapLoading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>地图加载中...</span>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="coordinates" type="primary" @click="centerMap">
          <el-icon><Aim /></el-icon>
          重新定位
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onBeforeUnmount } from 'vue'
import { LocationInformation, Loading, Aim } from '@element-plus/icons-vue'
import useAmap from '@/hooks/amap/useAmap'
import { getMapLocationImageUrl } from '@/utils/URLHelper'
import { SLMessage } from '@/utils/Message'

interface Props {
  modelValue: boolean
  address: string
  coordinates?: string // 格式: "经度,纬度"
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const mapLoading = ref(false)
let map: any = null
let marker: any = null

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.coordinates) {
    nextTick(() => {
      initMap()
    })
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化地图
const initMap = async () => {
  if (!props.coordinates) return

  mapLoading.value = true

  try {
    const coords = props.coordinates.split(',')
    if (coords.length !== 2) {
      SLMessage.error('坐标格式错误')
      return
    }

    const longitude = parseFloat(coords[0])
    const latitude = parseFloat(coords[1])

    if (isNaN(longitude) || isNaN(latitude)) {
      SLMessage.error('坐标数据无效')
      return
    }

    const { initAMap, setMarker } = useAmap()

    const mapConfig = {
      center: [longitude, latitude],
      mapLayer: ['卫星图层'],
      events: {
        complete: () => {
          mapLoading.value = false
        }
      }
    }

    map = await initAMap('mapContainer', mapConfig)

    // 添加标记点
    marker = setMarker([longitude, latitude], {
      icon: getMapLocationImageUrl()
    })

    // 设置地图缩放级别
    map.setZoom(16)

    // 添加信息窗口
    const infoWindow = new AMap.InfoWindow({
      content: `<div style="padding: 10px;">
        <h4 style="margin: 0 0 8px 0; color: #333;">事件位置</h4>
        <p style="margin: 0; color: #666; font-size: 12px;">地址: ${props.address}</p>
        <p style="margin: 4px 0 0 0; color: #666; font-size: 12px;">坐标: ${props.coordinates}</p>
      </div>`,
      offset: new AMap.Pixel(0, -30)
    })

    // 点击标记显示信息窗口
    marker.on('click', () => {
      infoWindow.open(map, marker.getPosition())
    })

    mapLoading.value = false

  } catch (error) {
    console.error('地图初始化失败:', error)
    SLMessage.error('地图加载失败，请稍后重试')
    mapLoading.value = false
  }
}

// 重新定位到中心
const centerMap = () => {
  if (!map || !marker) return

  try {
    map.setCenter(marker.getPosition())
    map.setZoom(16)
    SLMessage.success('已重新定位到事件位置')
  } catch (error) {
    console.error('重新定位失败:', error)
    SLMessage.error('重新定位失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 清理地图资源
  if (map) {
    map.destroy()
    map = null
    marker = null
  }
}

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (map) {
    map.destroy()
  }
})
</script>

<style lang="scss" scoped>
.map-location-container {
  .address-info {
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
    border-left: 4px solid #409eff;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #303133;
        min-width: 60px;
        font-size: 14px;
      }

      .value {
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        flex: 1;
      }
    }
  }

  .map-container {
    height: 450px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .no-location {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .location-icon {
        font-size: 64px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }

      .no-location-text {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 500;
        color: #909399;
      }

      .no-location-desc {
        margin: 0;
        font-size: 14px;
        color: #c0c4cc;
        text-align: center;
        max-width: 300px;
        line-height: 1.5;
      }
    }

    .map-view {
      width: 100%;
      height: 100%;
      position: relative;

      .map-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-icon {
          font-size: 24px;
          color: #409eff;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}

:deep(.map-location-dialog) {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
