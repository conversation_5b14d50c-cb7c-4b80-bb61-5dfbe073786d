<!-- 地图定位弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="事件地址定位"
    width="900px"
    :before-close="handleClose"
    class="map-location-dialog"
  >
    <div class="map-location-container">
      <div class="address-info">
        <div class="info-item">
          <span class="label">地址：</span>
          <span class="value">{{ address || '暂无地址信息' }}</span>
        </div>
        <div v-if="coordinates" class="info-item">
          <span class="label">坐标：</span>
          <span class="value">{{ coordinates }}</span>
        </div>
      </div>
      <div class="map-container">
        <div v-if="!coordinates" class="no-location">
          <el-icon class="location-icon"><LocationInformation /></el-icon>
          <p class="no-location-text">暂无位置信息</p>
          <p class="no-location-desc">该事件未设置地理坐标，无法在地图上显示位置</p>
        </div>
        <div v-else class="map-view">
          <div class="map-loading" v-if="mapLoading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>地图加载中...</span>
          </div>
          <div v-else id="mapViewDiv" class="map-container-inner"></div>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="coordinates" type="primary" @click="centerMap">
          <el-icon><Aim /></el-icon>
          重新定位
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onBeforeUnmount } from 'vue'
import { LocationInformation, Loading, Aim } from '@element-plus/icons-vue'
import { SLMessage } from '@/utils/Message'
import MapView from '@arcgis/core/views/MapView'
import Map from '@arcgis/core/Map'
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import PopupTemplate from '@arcgis/core/PopupTemplate'

interface Props {
  modelValue: boolean
  address: string
  coordinates?: string // 格式: "经度,纬度"
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const mapLoading = ref(false)
let mapView: MapView | null = null
let locationGraphic: Graphic | null = null

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.coordinates) {
    nextTick(() => {
      initMap()
    })
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化地图
const initMap = async () => {
  if (!props.coordinates) return

  mapLoading.value = true

  try {
    const coords = props.coordinates.split(',')
    if (coords.length !== 2) {
      SLMessage.error('坐标格式错误')
      mapLoading.value = false
      return
    }

    const longitude = parseFloat(coords[0])
    const latitude = parseFloat(coords[1])

    if (isNaN(longitude) || isNaN(latitude)) {
      SLMessage.error('坐标数据无效')
      mapLoading.value = false
      return
    }

    // 创建地图
    const map = new Map({
      basemap: 'streets-navigation-vector'
    })

    // 创建地图视图
    mapView = new MapView({
      container: 'mapViewDiv',
      map: map,
      center: [longitude, latitude],
      zoom: 15
    })

    // 等待地图加载完成
    await mapView.when()

    // 创建标记点
    const point = new Point({
      longitude: longitude,
      latitude: latitude
    })

    const markerSymbol = new SimpleMarkerSymbol({
      color: [226, 119, 40],
      outline: {
        color: [255, 255, 255],
        width: 2
      },
      size: 12
    })

    const popupTemplate = new PopupTemplate({
      title: '事件位置',
      content: `
        <div>
          <p><strong>地址：</strong>${props.address}</p>
          <p><strong>经度：</strong>${longitude}</p>
          <p><strong>纬度：</strong>${latitude}</p>
        </div>
      `
    })

    locationGraphic = new Graphic({
      geometry: point,
      symbol: markerSymbol,
      popupTemplate: popupTemplate
    })

    mapView.graphics.add(locationGraphic)
    mapLoading.value = false

  } catch (error) {
    console.error('地图初始化失败:', error)
    SLMessage.error('地图加载失败，请稍后重试')
    mapLoading.value = false
  }
}

// 重新定位到中心
const centerMap = () => {
  if (!props.coordinates || !mapView) return

  try {
    const coords = props.coordinates.split(',')
    const longitude = parseFloat(coords[0])
    const latitude = parseFloat(coords[1])

    mapView.goTo({
      center: [longitude, latitude],
      zoom: 15
    })

    SLMessage.success('已重新定位到事件位置')
  } catch (error) {
    console.error('重新定位失败:', error)
    SLMessage.error('重新定位失败')
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 清理地图资源
  if (mapView) {
    mapView.destroy()
    mapView = null
    locationGraphic = null
  }
}

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (mapView) {
    mapView.destroy()
  }
})
</script>

<style lang="scss" scoped>
.map-location-container {
  .address-info {
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 8px;
    border-left: 4px solid #409eff;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #303133;
        min-width: 60px;
        font-size: 14px;
      }

      .value {
        color: #606266;
        font-size: 14px;
        word-break: break-all;
        flex: 1;
      }
    }
  }

  .map-container {
    height: 450px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .no-location {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fafafa;

      .location-icon {
        font-size: 64px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }

      .no-location-text {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 500;
        color: #909399;
      }

      .no-location-desc {
        margin: 0;
        font-size: 14px;
        color: #c0c4cc;
        text-align: center;
        max-width: 300px;
        line-height: 1.5;
      }
    }

    .map-view {
      width: 100%;
      height: 100%;
      position: relative;

      .map-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-icon {
          font-size: 24px;
          color: #409eff;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
          color: #606266;
        }
      }

      .map-container-inner {
        width: 100%;
        height: 100%;
      }
    }
  }
}

:deep(.map-location-dialog) {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    padding: 16px 24px;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
