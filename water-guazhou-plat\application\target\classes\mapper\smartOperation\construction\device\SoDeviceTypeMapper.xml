<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.device.SoDeviceTypeMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           serial_id,
                           name,
                           parent_id,
                           level,
                           order_num,
                           remark,
                           creator,
                           create_time,
                           tenant_id<!--@sql from so_device_type -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.device.SoDeviceType">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="name" property="name"/>
        <result column="parent_id" property="parentId"/>
        <result column="level" property="level"/>
        <result column="order_num" property="orderNum"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_device_type
        <where>
            <if test="serialId != null">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="name != null">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>

    <update id="update">
        update so_device_type
        <set>
            <if test="serialId != null and serialId != ''">
                serial_id = #{serialId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            parent_id = #{parentId},
            <if test="level != null">
                level = if(#{parentId} is null, 1, (select i.level + 1 from so_device_type i where i.id = #{parentId})),
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_device_type
        set serial_id = #{serialId},
            name      = #{name},
            parent_id = #{parentId},
            level     = if(#{parentId} is null, 1, (select i.level + 1 from so_device_type i where i.id = #{parentId})),
            order_num = #{orderNum},
            remark    = #{remark}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_device_type(id,
                                   serial_id,
                                   name,
                                   parent_id,
                                   level,
                                   order_num,
                                   remark,
                                   creator,
                                   create_time,
                                   tenant_id)
        VALUES (#{id},
                #{serialId},
                #{name},
                #{parentId},
        <choose>
            <when test="parentId == null">
                <!--@ignoreSql-->
                1,
            </when>
            <otherwise>
                (select i.level + 1 from so_device_type i where i.id = #{parentId}),
            </otherwise>
        </choose>
        #{orderNum},
        #{remark},
        #{creator},
        #{createTime},
        #{tenantId})
    </insert>

    <select id="findRoots" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_device_type
        where parent_id is null
          and tenant_id = #{tenantId}
        order by order_num
    </select>

    <select id="findChildren" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_device_type
        where parent_id = #{parentId}
        order by order_num
    </select>

    <select id="getTreePathMap" resultType="java.util.Map">
        with recursive path(id, path) as (select id, (name)::text
                                          from so_device_type
                                          where parent_id is null
                                          union all
                                          select org.id, path.path || '>' || org.name
                                          from path,
                                               so_device_type org
                                          where path.id = org.parent_id)
        select *
        from path;
    </select>

    <select id="existsBySerialId" resultType="boolean">
        select count(1) > 0
        from so_device_type
        where serial_id = #{serialId}
          and tenant_id = #{tenantId};
    </select>

    <select id="getDepthBySerialId" resultType="int">
        select level
        from so_device_type
        where serial_id = #{serialId}
          and tenant_id = #{tenantId};
    </select>

    <!--@formatter:off-->
    <select id="getParentId" resultType="java.lang.String">
        select parent_id from so_device_type where id = #{id}
    </select>

    <select id="getDepth" resultType="java.lang.Integer">
        select level from so_device_type where id = #{id}
    </select>

    <select id="getSerialId" resultType="java.lang.String">
        select serial_id from so_device_type where id = #{id}
    </select>

    <select id="selectCountBySerialIdAndTenantId" resultType="int">
        select count(1) from so_device_type
        where serial_id = #{serialId} and tenant_id = #{tenantId};
    </select>

    <select id="selectCountByIdAndSerialId" resultType="int">
        select count(1) from so_device_type
        where id = #{id} and serial_id = #{serialId};
    </select>

    <select id="canBeDelete" resultType="boolean">
        select
        <!--下有类型-->
        (select count(1) = 0 from so_device_type type where type.parent_id = #{id}) and
        <!--下有设备-->
        (select count(1) = 0 from so_device device where
        device.serial_id = (select i.serial_id from so_device_type i where i.id = #{id})
        and device.tenant_id = #{tenantId})
    </select>
    <!--@formatter:on-->

    <select id="getIdBySerialId" resultType="java.lang.String">
        select id
        from so_device_type
        where serial_id=#{serialId}
        and tenant_id = #{tenantId}
    </select>
</mapper>