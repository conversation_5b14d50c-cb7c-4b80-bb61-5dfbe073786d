<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeCopyDataCorrectRecordsMapper">

    <select id="getCopyCorrectRecords" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeCopyDataCorrectRecords">
        select a.*, b.code as custCode, b.name as custName, b.phone, copyData.ym
        from tb_pipe_copy_data_correct_records a
        left join revenue.tb_pipe_copy_data_read_meter_data copyData on a.copy_id = copyData.id
        left join revenue.tb_water_meter twm on copyData.meter_id = twm.meter_code and a.tenant_id = twm.tenant_id
        left join revenue.tb_cust_info b on twm.cust_code = b.code and twm.tenant_id = b.tenant_id
        left join tb_pipe_partition_cust tppc on b.code = tppc.cust_code and a.tenant_id = tppc.tenant_id
        where a.tenant_id = #{param.tenantId} and a.type = '1'
        <if test="param.partitionId != null and param.partitionId != ''">
            and tppc.partition_id = #{param.partitionId}
        </if>
        <if test="param.custName != null and param.custName != ''">
            and b.name like '%' || #{param.custName} || '%'
        </if>
        order by a.create_time desc
    </select>
    <select id="getSupplyTotalRecords" resultType="org.thingsboard.server.dao.model.DTO.PartitionSupplyCorrectRecordsDTO">
        select parti.id as partitionId, parti.name as partitionName, device.name as deviceName, a.id, a.correct_water, use.first_name as updateUser, a.create_time,
        (case
        when flow.type = '1' then to_char(flow.collect_time, 'YYYY-MM-DD HH24:MI:SS')
        when flow.type = '2' then to_char(flow.collect_time, 'YYYY-MM-DD')
        when flow.type = '3' then to_char(flow.collect_time, 'YYYY-MM') end) as collectTime
        from tb_pipe_copy_data_correct_records a
        left join tb_pipe_partition_total_flow flow on a.copy_id = flow.id
        left join tb_pipe_partition_mount mount on flow.device_id = mount.device_id
        left join tb_pipe_partition parti on parti.id = mount.partition_id
        left join device device on device.id = flow.device_id
        left join tb_user use on use.id = a.creator
        where a.tenant_id = #{param.tenantId} and a.type = '2'
        <if test="param.partitionId != null and param.partitionId != ''">
            and mount.partition_id = #{param.partitionId}
        </if>
        <if test="param.partitionName != null and param.partitionName != ''">
            and parti.name like '%' || #{param.partitionName} || '%'
        </if>
        order by a.create_time desc
    </select>

</mapper>