<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.workOrder.WorkOrderDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->detail.id,
                           detail.type detail_type,
                           detail.process_user_id,
                           detail.process_time,
                           detail.process_remark,
                           detail.process_additional_info,
                           detail.next_process_user_id,
                           detail.main_id<!--@sql from work_order_details detail-->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.workOrder.WorkOrderDetail">
        <result column="id" property="id"/>
        <result column="detail_type" property="type"/>
        <result column="process_user_id" property="processUserId"/>
        <result column="process_time" property="processTime"/>
        <result column="process_remark" property="processRemark"/>
        <result column="process_additional_info" property="processAdditionalInfo"/>
        <result column="next_process_user_id" property="nextProcessUserId"/>
        <result column="main_id" property="mainId"/>
    </resultMap>

    <select id="findAllDetails" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_order_details detail
        where main_id = #{id}
        order by detail.process_time
    </select>

    <select id="getStagesByWorkOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from work_order_details detail
        where main_id = #{id}
        order by detail.process_time
    </select>

    <select id="getDetails" resultType="org.thingsboard.server.dao.model.DTO.WorkOrderDetailDTO">
        select <include refid="Base_Column_List"/>, detail.type, b.first_name as process_user_name, c.name as process_user_department
        from work_order_details detail
        left join tb_user b on detail.process_user_id = b.id
        left join tb_department c on b.department_id = c.id
        where main_id = #{workOrderId}
        order by detail.process_time
    </select>
</mapper>