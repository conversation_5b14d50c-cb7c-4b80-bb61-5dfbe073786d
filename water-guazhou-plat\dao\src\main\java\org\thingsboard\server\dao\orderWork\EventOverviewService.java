package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.util.imodel.IModel;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;
import org.thingsboard.server.dao.util.imodel.response.EventOverviewStatisticsResponse;

/**
 * 事件总览服务接口
 * 
 * <AUTHOR>
 */
public interface EventOverviewService {

    /**
     * 分页查询事件总览
     * 
     * @param request 查询参数
     * @return 分页结果
     */
    IPage<WorkOrder> findEventOverviewByPage(EventOverviewPageRequest request);

    /**
     * 根据ID查询事件详情
     * 
     * @param id 事件ID
     * @param tenantId 租户ID
     * @return 事件详情
     */
    WorkOrder findEventById(String id, String tenantId);

    /**
     * 更新事件坐标信息
     * 
     * @param id 事件ID
     * @param coordinate 坐标信息（格式：经度,纬度）
     * @param tenantId 租户ID
     */
    void updateEventCoordinate(String id, String coordinate, String tenantId);

    /**
     * 获取事件统计信息
     * 
     * @param fromTime 开始时间
     * @param toTime 结束时间
     * @param source 来源
     * @param tenantId 租户ID
     * @return 统计信息
     */
    EventOverviewStatisticsResponse getEventStatistics(Long fromTime, Long toTime, String source, String tenantId);

    /**
     * 批量更新事件坐标
     * 
     * @param model 批量更新参数
     * @param tenantId 租户ID
     */
    void batchUpdateCoordinates(IModel model, String tenantId);

    /**
     * 根据地址获取坐标
     * 
     * @param address 地址
     * @return 坐标信息（格式：经度,纬度）
     */
    String getCoordinateByAddress(String address);

    /**
     * 根据关键词搜索事件
     * 
     * @param keyword 关键词
     * @param tenantId 租户ID
     * @return 搜索结果
     */
    IPage<WorkOrder> searchEventsByKeyword(String keyword, String tenantId);

    /**
     * 获取热点地区统计
     * 
     * @param fromTime 开始时间
     * @param toTime 结束时间
     * @param tenantId 租户ID
     * @return 热点地区统计
     */
    Object getHotspotStatistics(Long fromTime, Long toTime, String tenantId);
}
