package org.thingsboard.server.dao.fault;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.common.data.utils.DateUtils;
import org.thingsboard.server.dao.model.sql.deviceManage.DeviceType;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskC;
import org.thingsboard.server.dao.model.sql.fault.FaultTaskM;
import org.thingsboard.server.dao.sql.deviceType.DeviceTypeMapper;
import org.thingsboard.server.dao.sql.fault.FaultTaskCMapper;
import org.thingsboard.server.dao.sql.fault.FaultTaskMMapper;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @since v1.0.0 2022-08-24
 */
@Slf4j
@Service
@Transactional
public class FaultTaskMServiceImpl implements FaultTaskMService {

    @Autowired
    private FaultTaskMMapper faultTaskMMapper;

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private FaultTaskCMapper faultTaskCMapper;

    @Override
    public PageData getList(String code, String name, String planName, String teamName, String userName, String status, String auditStatus, Date startStartTime, Date startEndTime, Date endStartTime, Date endEndTime, int page, int size, String tenantId) {

        List<FaultTaskM> faultTaskMList = faultTaskMMapper.getList(code, name, planName, teamName, userName, status, auditStatus, startStartTime, startEndTime, endStartTime, endEndTime, page, size, tenantId);

        int total = faultTaskMMapper.getListCount(code, name, planName, teamName, userName, status, auditStatus, startStartTime, startEndTime, endStartTime, endEndTime, tenantId);

        return new PageData(total, faultTaskMList);
    }

    @Override
    public FaultTaskM getDetail(String mainId) {
        FaultTaskM faultTaskM = faultTaskMMapper.getById(mainId);
        List<FaultTaskC> faultTaskCList = faultTaskCMapper.getList(faultTaskM.getId());
        // 所属分类链表
        for (FaultTaskC faultTaskC : faultTaskCList) {
            this.setType(faultTaskC);
        }

        faultTaskM.setFaultTaskCList(faultTaskCList);

        return faultTaskM;
    }

    @Override
    public FaultTaskM save(FaultTaskM faultTaskM) {

        if (StringUtils.isBlank(faultTaskM.getId())) {
            faultTaskM.setStatus("0");
            faultTaskM.setCreateTime(new Date());
            faultTaskM.setCode("BY" + new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date()));;
            faultTaskM.setAuditStatus("0");;

            faultTaskMMapper.insert(faultTaskM);
        } else {
            faultTaskMMapper.updateById(faultTaskM);
        }

        Map deleteMap = new HashMap<>();
        deleteMap.put("main_id", faultTaskM.getId());
        faultTaskCMapper.deleteByMap(deleteMap);

        if (faultTaskM.getFaultTaskCList() != null) {
            for (FaultTaskC faultTaskC : faultTaskM.getFaultTaskCList()) {
                faultTaskC.setMainId(faultTaskM.getId());
                faultTaskC.setTenantId(faultTaskM.getTenantId());
                faultTaskCMapper.insert(faultTaskC);
            }
        }

        return faultTaskM;
    }

    @Override
    public IstarResponse delete(List<String> ids) {
        faultTaskMMapper.deleteBatchIds(ids);

        // 删除子表
        QueryWrapper<FaultTaskC> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("main_id", ids);
        faultTaskCMapper.delete(queryWrapper);

        return IstarResponse.ok("删除成功");
    }

    @Override
    public void reviewer(FaultTaskM faultTaskM) {
        FaultTaskM faultTaskM1 = new FaultTaskM();
        faultTaskM1.setId(faultTaskM.getId());
        faultTaskM1.setAuditor(faultTaskM.getAuditor());
        faultTaskM1.setAuditRemark(faultTaskM.getAuditRemark());
        faultTaskM1.setAuditStatus(faultTaskM.getAuditStatus());
        faultTaskM1.setAuditTime(new Date());

        faultTaskMMapper.updateById(faultTaskM1);
    }

    @Override
    public void changeStatus(FaultTaskM faultTaskM) {
        FaultTaskM faultTaskM1 = new FaultTaskM();
        faultTaskM1.setId(faultTaskM.getId());
        faultTaskM1.setStatus(faultTaskM.getStatus());

        faultTaskMMapper.updateById(faultTaskM1);
    }

    @Override
    public List<FaultTaskM> findAll() {
        return faultTaskMMapper.selectByMap(new HashMap<>());
    }

    @Override
    public List statistics(Long startTime, Long endTime, String tenantId) {
        return faultTaskMMapper.statistics(startTime == null ? null : new Date(startTime), endTime == null ? null : new Date(endTime), tenantId);
    }

    @Override
    public boolean checkAuditor(String id, String userId) {
        FaultTaskM faultTaskM = faultTaskMMapper.selectById(id);
        if (faultTaskM.getAuditor() != null && !userId.equals(faultTaskM.getAuditor())) {
            return false;
        }
        return true;
    }


    private void setType(FaultTaskC faultTaskC) {
        if (StringUtils.isBlank(faultTaskC.getTypeId())) {
            faultTaskC.setLinkedType("-");
            return;
        }
        String linkedType = "-";
        String topType = "-";
        DeviceType deviceType = null;
        String parentId = faultTaskC.getTypeId();
        for (int i = 0; i < 5; i++) {
            deviceType = deviceTypeMapper.selectById(parentId);
            if (deviceType == null) {
                break;
            }
            linkedType = deviceType.getName() + ">" + linkedType;
            if (StringUtils.isBlank(deviceType.getParentId())) {
                topType = deviceType.getName();
                break;
            }
            parentId = deviceType.getParentId();
        }
        if (linkedType.length() >= 2) {
            linkedType = linkedType.substring(0, linkedType.length() - 2);
        }

        faultTaskC.setLinkedType(linkedType);
        faultTaskC.setTopType(topType);
        faultTaskC.setType(linkedType);
        try {
            if (linkedType.contains(">")) {
                faultTaskC.setType(linkedType.substring(linkedType.lastIndexOf(">") + 1));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 上次操作时间
        FaultTaskC faultTaskC1 = faultTaskCMapper.selectFirstByDeviceLabelCode(faultTaskC.getDeviceLabelCode(), faultTaskC.getCreateTime());
        if (faultTaskC1 != null) {
            faultTaskC.setLastModifyTime(faultTaskC1.getCreateTime());
        }
    }


    private boolean checkTimeIsToday(Date nextExecuteTime) throws ParseException {
        String string = DateUtils.date2Str(new Date(), DateUtils.DATE_FORMATE_DAY);
        SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMATE_DEFAULT);
//        Date todayStart = dateFormat.parse(string + " 00:00:00");
        Date todayEnd = dateFormat.parse(string + " 23:59:59");

        if (/*nextExecuteTime.getTime() > todayStart.getTime() && */nextExecuteTime.getTime() < todayEnd.getTime()) {
            return true;
        }

        return false;
    }


}
