<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.guard.GuardPlaceMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        address,
        order_num,
        tenant_id
        <!--@sql from guard_place -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.guard.GuardPlace">
        <result column="id" property="id"/>
        <result column="address" property="address"/>
        <result column="order_num" property="orderNum"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from guard_place
        <where>
            and tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>

    <update id="updateFully">
        update guard_place
        set address   = #{address},
            order_num = #{orderNum}
        where id = #{id}
    </update>
</mapper>