<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.plan.PlanDetailMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
        main_id,
        serial_id,
        shelves_id,
        (device_rest_storage_count_by_serial_id_and_shelves_id(serial_id, shelves_id, true, tenant_id)) as count,
        tenant_id<!--@sql from plan_detail -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.plan.PlanDetail">
        <result column="id" property="id"/>
        <result column="main_id" property="mainId"/>
        <result column="serial_id" property="serialId"/>
        <result column="count" property="count"/>
        <result column="shelves_id" property="shelvesId"/>
        <result column="tenant_id" property="tenantId"/>
        <association property="deviceInfoResponse"
                     javaType="org.thingsboard.server.dao.model.sql.store.DeviceInfoResponse"
                     column="{serialId=serial_id,tenantId=tenant_id}"
                     select="org.thingsboard.server.dao.sql.deviceType.DeviceMapper.getInfoBySerialId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from plan_detail
        <where>
            <if test="mainId != null and mainId != ''">
                and main_id = #{mainId}
            </if>
            <if test="serialId != null and serialId != ''">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="shelvesId != null and shelvesId != ''">
                and shelves_id = #{shelvesId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update plan_detail
        <set>
            <if test="id != null">
                id = #{id},
            </if>
            <if test="serialId != null">
                serial_id = #{serialId},
            </if>
            <if test="shelvesId != null">
                shelves_id = #{shelvesId},
            </if>
            tenant_id = #{tenantId},
        </set>
        where id = #{id}
    </update>

    <insert id="saveAll">
        INSERT INTO plan_detail(id,
        main_id,
        serial_id,
        shelves_id,
        tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
            #{element.mainId},
            #{element.serialId},
            #{element.shelvesId},
            #{element.tenantId})
        </foreach>
    </insert>

    <update id="updateAll">
        update plan_detail
        <set>
            serial_id  = valueTable.serialId,
            shelves_id = valueTable.shelvesId
        </set>
        FROM (
        VALUES
        <foreach collection="list" item="element" separator=",">
            (#{element.id},
             #{element.serialId},
             #{element.shelvesId})
        </foreach>
        ) as valueTable(id, serialId, shelvesId)
        where id = valueTable.id
    </update>

    <delete id="deleteAllByMainId">
        delete
        from plan_detail
        where main_id = #{id}
        <if test="idList != null and idList.size() != 0">
            and id not in
            <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>
</mapper>