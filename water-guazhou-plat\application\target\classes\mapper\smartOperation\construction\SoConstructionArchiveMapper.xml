<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionArchiveMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        archive.id,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        archive.remark,
        archive.attachments,
        archive.creator,
        archive.create_time,
        archive.update_user,
        archive.update_time,
        construction.tenant_id<!--@sql from so_construction_archive archive, so_construction construction -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionArchive">
        <result column="id" property="id"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_archive archive
                           on archive.construction_code = construction.code and
                              archive.tenant_id = construction.tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="projectStartTimeFrom != null">
                <!--@formatter:off-->
                and (select start_time >= #{projectStartTimeFrom} from so_project where code = project_code and tenant_id = #{tenantId})
                <!--@formatter:on-->
            </if>
            <if test="projectStartTimeTo != null">
                <!--@formatter:off-->
                and (select start_time &lt;= #{projectStartTimeFrom} from so_project where code = project_code and tenant_id = #{tenantId})
                <!--@formatter:on-->
            </if>
            <if test="fromTime != null">
                and archive.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and archive.create_time &lt;= #{toTime}
            </if>
            <!--@formatter:off-->
            <!--            and (select count(1) = 0 from so_construction_apply-->
            <!--            where construction_code = construction.code and status = #{processingStatus} and tenant_id = #{tenantId})-->
            and (select count(1) = 0 from so_construction_task_info
            where construction_code = construction.code and status = #{processingStatus} and tenant_id = #{tenantId})
            <!--@formatter:on-->
            and construction.tenant_id = #{tenantId}
        </where>
        order by construction.create_time desc
    </select>

    <update id="update">
        update so_construction_archive
        <set>
            <!--            <if test="constructionCode != null">-->
            <!--                construction_code = #{constructionCode},-->
            <!--            </if>-->
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_user = #{updateUser},
            update_time = #{updateTime}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_archive
        set remark      = #{remark},
            attachments = #{attachments},
            update_user = #{updateUser},
            update_time = #{updateTime}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_construction_archive(id,
                                            construction_code,
                                            remark,
                                            attachments,
                                            creator,
                                            create_time,
                                            update_user,
                                            update_time,
                                            tenant_id)
        VALUES (#{id},
                #{constructionCode},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="isComplete" resultType="boolean">
        <!--@formatter:off-->
        select (select count(1) = 0 from so_construction_apply
                where construction_code = #{code} and tenant_id = #{tenantId} and status = #{processingStatus})
           and (select count(1) = 0 from so_construction_task_info
                where construction_code = #{code} and tenant_id = #{tenantId} and status = #{processingStatus})
        <!--@formatter:on-->
    </select>

    <select id="getIdByConstructionCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_construction_archive
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>