<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.safeProduction.ProductionTaskMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.smartProduction.safeProduction.ProductionTask">
        select
        a.*,d.first_name as "execUserName", f.first_name as "reviewerName",
        e.first_name as "creatorName",
        b.title AS "workOrderName", b.serial_no as "workOrderCode", b.create_time as "realStartTime",
        b.complete_time as "realEndTime",
        c.name AS "levelName"
        from sp_production_task a
        LEFT JOIN work_order b ON a.work_order_id = b.id
        LEFT JOIN work_order_emergency_level c ON a.level = c.id
        left join tb_user d on a.exec_user = d.id
        left join tb_user e on a.creator = e.id
       left join tb_user f on a.reviewer = f.id
        <where>
            <if test="param.code != null and param.code != ''">
                and a.code like '%'||#{param.code}||'%'
            </if>
            <if test="param.name != null and param.name != ''">
                and a.name like '%'||#{param.name}||'%'
            </if>
            <if test="param.type != null and param.type != ''">
                and a.type = #{param.type}
            </if>
            <if test="param.execUser != null and param.execUser != ''">
                and a.exec_user = #{param.execUser}
            </if>
            <if test="param.status != null and param.status != ''">
                and a.status in
                <foreach collection="param.status.split(',')" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="param.tenantId != null and param.tenantId != ''">
                and a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.start != null">
                and a.create_time >= to_timestamp(#{param.start} / 1000)
            </if>
            <if test="param.end != null">
                and a.create_time &lt;= to_timestamp(#{param.end} / 1000)
            </if>
        </where>
        order by a.create_time desc
    </select>
</mapper>