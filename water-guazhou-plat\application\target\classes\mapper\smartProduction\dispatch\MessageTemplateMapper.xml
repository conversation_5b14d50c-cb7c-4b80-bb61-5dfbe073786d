<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.MessageTemplateMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           content,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_message_template -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.MessageTemplate">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="content" property="content"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_message_template
        <where>
            <if test="keyword != null and keyword != ''">
                and (
                    str_like("name", #{keyword})
                    or str_like(content, #{keyword})
                )
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_message_template
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>