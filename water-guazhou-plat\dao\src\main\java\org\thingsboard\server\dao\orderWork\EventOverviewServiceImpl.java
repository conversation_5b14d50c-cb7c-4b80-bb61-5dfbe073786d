package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.mapper.workOrder.EventOverviewMapper;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件总览服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventOverviewServiceImpl implements EventOverviewService {

    @Autowired
    private EventOverviewMapper eventOverviewMapper;

    @Override
    public IPage<EventOverview> findEventOverviewByPage(EventOverviewPageRequest request) {
        Page<EventOverview> page = new Page<>(request.getPage(), request.getSize());
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }

        return eventOverviewMapper.selectPage(page, queryWrapper);
    }

    @Override
    public EventOverview findEventOverviewById(String id, String tenantId) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }
        
        return eventOverviewMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateEventCoordinate(String id, String coordinate, String coordinateName, String tenantId) {
        if (!isValidCoordinate(coordinate)) {
            log.warn("Invalid coordinate format: {}", coordinate);
            return false;
        }
        
        EventOverview eventOverview = new EventOverview();
        eventOverview.setId(id);
        eventOverview.setCoordinate(coordinate);
        eventOverview.setCoordinateName(coordinateName);
        eventOverview.setUpdateTime(new Date());
        
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (StringUtils.hasText(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }
        
        int result = eventOverviewMapper.update(eventOverview, queryWrapper);
        return result > 0;
    }

    @Override
    public int batchUpdateEventCoordinates(List<Map<String, String>> updates, String tenantId) {
        int successCount = 0;
        for (Map<String, String> update : updates) {
            String id = update.get("id");
            String coordinate = update.get("coordinate");
            String coordinateName = update.get("coordinateName");
            
            if (updateEventCoordinate(id, coordinate, coordinateName, tenantId)) {
                successCount++;
            }
        }
        return successCount;
    }

    @Override
    public Map<String, Object> getEventStatistics(String tenantId, String projectId) {
        return eventOverviewMapper.getEventStatistics(tenantId, projectId);
    }

    @Override
    public List<Map<String, Object>> getEventHotspotStatistics(String tenantId, int limit) {
        return eventOverviewMapper.getEventHotspotStatistics(tenantId, limit);
    }

    @Override
    public List<Map<String, Object>> getEventTrendStatistics(String tenantId, int days) {
        return eventOverviewMapper.getEventTrendStatistics(tenantId, days);
    }

    @Override
    public List<Map<String, Object>> getEventStatusDistribution(String tenantId, String projectId) {
        return eventOverviewMapper.getEventStatusDistribution(tenantId, projectId);
    }

    @Override
    public Map<String, Object> getProcessEfficiencyStatistics(String tenantId, int days) {
        return eventOverviewMapper.getProcessEfficiencyStatistics(tenantId, days);
    }

    @Override
    public String geocodeAddress(String address) {
        // TODO: 集成地理编码服务（如高德地图API）
        log.info("Geocoding address: {}", address);
        return null;
    }

    @Override
    public String reverseGeocodeCoordinate(String coordinate) {
        // TODO: 集成逆地理编码服务
        log.info("Reverse geocoding coordinate: {}", coordinate);
        return null;
    }

    @Override
    public boolean isValidCoordinate(String coordinate) {
        if (!StringUtils.hasText(coordinate)) {
            return false;
        }
        
        try {
            String[] parts = coordinate.split(",");
            if (parts.length != 2) {
                return false;
            }
            
            double longitude = Double.parseDouble(parts[0].trim());
            double latitude = Double.parseDouble(parts[1].trim());
            
            // 检查经纬度范围
            return longitude >= -180 && longitude <= 180 && latitude >= -90 && latitude <= 90;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @Override
    public double calculateDistance(String coordinate1, String coordinate2) {
        if (!isValidCoordinate(coordinate1) || !isValidCoordinate(coordinate2)) {
            return -1;
        }
        
        try {
            String[] coord1 = coordinate1.split(",");
            String[] coord2 = coordinate2.split(",");
            
            double lon1 = Double.parseDouble(coord1[0].trim());
            double lat1 = Double.parseDouble(coord1[1].trim());
            double lon2 = Double.parseDouble(coord2[0].trim());
            double lat2 = Double.parseDouble(coord2[1].trim());
            
            return calculateHaversineDistance(lat1, lon1, lat2, lon2);
        } catch (Exception e) {
            log.error("Error calculating distance", e);
            return -1;
        }
    }

    @Override
    public List<EventOverview> findEventsInRadius(String centerCoordinate, double radiusMeters, String tenantId) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.isNotNull("coordinate");
        queryWrapper.ne("coordinate", "");
        
        List<EventOverview> allEvents = eventOverviewMapper.selectList(queryWrapper);
        
        return allEvents.stream()
                .filter(event -> {
                    double distance = calculateDistance(centerCoordinate, event.getCoordinate());
                    return distance >= 0 && distance <= radiusMeters;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<EventOverview> exportEventOverview(EventOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = buildQueryWrapper(request);
        
        // 设置排序
        if (StringUtils.hasText(request.getOrderBy())) {
            String orderBy = convertOrderByField(request.getOrderBy());
            if ("DESC".equalsIgnoreCase(request.getOrderDirection())) {
                queryWrapper.orderByDesc(orderBy);
            } else {
                queryWrapper.orderByAsc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        
        return eventOverviewMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getProcessDurationStatistics(String tenantId, int days) {
        return eventOverviewMapper.getProcessDurationStatistics(tenantId, days);
    }

    @Override
    public List<EventOverview> getOverdueEvents(String tenantId, int limit) {
        return eventOverviewMapper.getOverdueEvents(tenantId, limit);
    }

    @Override
    public List<EventOverview> getHighPriorityEvents(String tenantId, int limit) {
        return eventOverviewMapper.getHighPriorityEvents(tenantId, limit);
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<EventOverview> buildQueryWrapper(EventOverviewPageRequest request) {
        QueryWrapper<EventOverview> queryWrapper = new QueryWrapper<>();
        
        // 租户过滤
        if (StringUtils.hasText(request.getTenantId())) {
            queryWrapper.eq("tenant_id", request.getTenantId());
        }
        
        // 关键词搜索
        if (StringUtils.hasText(request.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like("work_order_id", request.getKeyword())
                    .or().like("title", request.getKeyword())
                    .or().like("address", request.getKeyword())
                    .or().like("remark", request.getKeyword())
            );
        }
        
        // 状态过滤
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq("status", request.getStatus());
        }
        
        // 状态区间过滤
        EnumSet<WorkOrderStatus> statusRange = request.getStatusRangeSet();
        if (statusRange != null && !statusRange.isEmpty()) {
            List<String> statusList = statusRange.stream()
                    .map(Enum::name)
                    .collect(Collectors.toList());
            queryWrapper.in("status", statusList);
        }
        
        // 其他字段过滤
        if (StringUtils.hasText(request.getSource())) {
            queryWrapper.eq("source", request.getSource());
        }
        
        if (StringUtils.hasText(request.getLevel())) {
            queryWrapper.eq("level", request.getLevel());
        }
        
        if (StringUtils.hasText(request.getType())) {
            queryWrapper.eq("type", request.getType());
        }
        
        if (StringUtils.hasText(request.getOrganizerId())) {
            queryWrapper.eq("organizer_id", request.getOrganizerId());
        }
        
        if (StringUtils.hasText(request.getProcessUserId())) {
            queryWrapper.eq("process_user_id", request.getProcessUserId());
        }
        
        if (StringUtils.hasText(request.getProjectId())) {
            queryWrapper.eq("project_id", request.getProjectId());
        }
        
        // 坐标过滤
        if (Boolean.TRUE.equals(request.getHasCoordinate())) {
            queryWrapper.isNotNull("coordinate");
            queryWrapper.ne("coordinate", "");
        }
        
        // 时间范围过滤
        if (request.getCreateTimeFrom() != null) {
            queryWrapper.ge("create_time", request.getCreateTimeFrom());
        }
        
        if (request.getCreateTimeTo() != null) {
            queryWrapper.le("create_time", request.getCreateTimeTo());
        }
        

        
        return queryWrapper;
    }
    
    /**
     * 转换排序字段名
     */
    private String convertOrderByField(String orderBy) {
        // 将驼峰命名转换为下划线命名
        return orderBy.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    

    
    /**
     * 计算两点间距离（Haversine公式）
     */
    private double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
}
