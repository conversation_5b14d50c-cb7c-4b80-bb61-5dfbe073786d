package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.sql.workOrder.NewlyWorkOrderMapper;
import org.thingsboard.server.dao.util.imodel.IModel;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;
import org.thingsboard.server.dao.util.imodel.response.EventOverviewStatisticsResponse;

import java.util.*;

/**
 * 事件总览服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class EventOverviewServiceImpl implements EventOverviewService {

    @Autowired
    private NewlyWorkOrderMapper workOrderMapper;

    @Override
    public IPage<WorkOrder> findEventOverviewByPage(EventOverviewPageRequest request) {
        Page<WorkOrder> page = new Page<>(request.getPage(), request.getSize());
        
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        
        // 租户过滤
        if (!StringUtils.isEmpty(request.getTenantId())) {
            queryWrapper.eq("tenant_id", request.getTenantId());
        }
        
        // 时间范围过滤
        if (request.getFromTime() != null) {
            queryWrapper.ge("create_time", new Date(request.getFromTime()));
        }
        if (request.getToTime() != null) {
            queryWrapper.le("create_time", new Date(request.getToTime()));
        }
        
        // 来源过滤
        if (!StringUtils.isEmpty(request.getSource())) {
            queryWrapper.eq("source", request.getSource());
        }
        
        // 创建人过滤
        if (!StringUtils.isEmpty(request.getOrganizerId())) {
            queryWrapper.eq("organizer_id", request.getOrganizerId());
        }
        
        // 状态过滤
        if (!StringUtils.isEmpty(request.getStatus())) {
            queryWrapper.eq("status", request.getStatus());
        }
        
        // 标题模糊查询
        if (!StringUtils.isEmpty(request.getTitle())) {
            queryWrapper.like("title", request.getTitle());
        }
        
        // 地址模糊查询
        if (!StringUtils.isEmpty(request.getAddress())) {
            queryWrapper.and(wrapper -> wrapper
                .like("address", request.getAddress())
                .or()
                .like("upload_address", request.getAddress())
            );
        }
        
        // 事件类型过滤
        if (!StringUtils.isEmpty(request.getType())) {
            queryWrapper.eq("type", request.getType());
        }
        
        // 紧急程度过滤
        if (!StringUtils.isEmpty(request.getLevel())) {
            queryWrapper.eq("level", request.getLevel());
        }
        
        // 处理人过滤
        if (!StringUtils.isEmpty(request.getProcessUserId())) {
            queryWrapper.eq("process_user_id", request.getProcessUserId());
        }
        
        // 是否只查询有坐标的事件
        if (request.getHasCoordinate() != null && request.getHasCoordinate()) {
            queryWrapper.isNotNull("coordinate").ne("coordinate", "");
        }
        
        // 关键词搜索
        if (!StringUtils.isEmpty(request.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like("title", request.getKeyword())
                .or()
                .like("address", request.getKeyword())
                .or()
                .like("upload_address", request.getKeyword())
                .or()
                .like("remark", request.getKeyword())
            );
        }
        
        // 排序
        if ("ASC".equalsIgnoreCase(request.getOrderDirection())) {
            queryWrapper.orderByAsc(request.getOrderBy());
        } else {
            queryWrapper.orderByDesc(request.getOrderBy());
        }
        
        return workOrderMapper.selectPage(page, queryWrapper);
    }

    @Override
    public WorkOrder findEventById(String id, String tenantId) {
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (!StringUtils.isEmpty(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }
        return workOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateEventCoordinate(String id, String coordinate, String tenantId) {
        WorkOrder workOrder = new WorkOrder();
        workOrder.setId(id);
        workOrder.setCoordinate(coordinate);
        workOrder.setUpdateTime(new Date());
        
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", id);
        if (!StringUtils.isEmpty(tenantId)) {
            queryWrapper.eq("tenant_id", tenantId);
        }
        
        workOrderMapper.update(workOrder, queryWrapper);
    }

    @Override
    public EventOverviewStatisticsResponse getEventStatistics(Long fromTime, Long toTime, String source, String tenantId) {
        QueryWrapper<WorkOrder> baseWrapper = new QueryWrapper<>();
        
        // 租户过滤
        if (!StringUtils.isEmpty(tenantId)) {
            baseWrapper.eq("tenant_id", tenantId);
        }
        
        // 时间范围过滤
        if (fromTime != null) {
            baseWrapper.ge("create_time", new Date(fromTime));
        }
        if (toTime != null) {
            baseWrapper.le("create_time", new Date(toTime));
        }
        
        // 来源过滤
        if (!StringUtils.isEmpty(source)) {
            baseWrapper.eq("source", source);
        }
        
        // 总数
        Long total = workOrderMapper.selectCount(baseWrapper);
        
        // 各状态统计
        QueryWrapper<WorkOrder> pendingWrapper = baseWrapper.clone();
        pendingWrapper.in("status", WorkOrderStatus.PENDING.name(), WorkOrderStatus.ASSIGN.name());
        Long pending = workOrderMapper.selectCount(pendingWrapper);
        
        QueryWrapper<WorkOrder> processingWrapper = baseWrapper.clone();
        processingWrapper.in("status", WorkOrderStatus.RESOLVING.name(), WorkOrderStatus.ARRIVING.name(), WorkOrderStatus.PROCESSING.name());
        Long processing = workOrderMapper.selectCount(processingWrapper);
        
        QueryWrapper<WorkOrder> completedWrapper = baseWrapper.clone();
        completedWrapper.in("status", WorkOrderStatus.COMPLETE.name(), WorkOrderStatus.APPROVED.name());
        Long completed = workOrderMapper.selectCount(completedWrapper);
        
        QueryWrapper<WorkOrder> terminatedWrapper = baseWrapper.clone();
        terminatedWrapper.in("status", WorkOrderStatus.TERMINATED.name(), WorkOrderStatus.REJECTED.name());
        Long terminated = workOrderMapper.selectCount(terminatedWrapper);
        
        // 今日新增
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        
        QueryWrapper<WorkOrder> todayWrapper = baseWrapper.clone();
        todayWrapper.ge("create_time", today.getTime());
        Long todayNew = workOrderMapper.selectCount(todayWrapper);
        
        // 本周新增
        Calendar weekStart = Calendar.getInstance();
        weekStart.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        weekStart.set(Calendar.HOUR_OF_DAY, 0);
        weekStart.set(Calendar.MINUTE, 0);
        weekStart.set(Calendar.SECOND, 0);
        weekStart.set(Calendar.MILLISECOND, 0);
        
        QueryWrapper<WorkOrder> weekWrapper = baseWrapper.clone();
        weekWrapper.ge("create_time", weekStart.getTime());
        Long weekNew = workOrderMapper.selectCount(weekWrapper);
        
        // 本月新增
        Calendar monthStart = Calendar.getInstance();
        monthStart.set(Calendar.DAY_OF_MONTH, 1);
        monthStart.set(Calendar.HOUR_OF_DAY, 0);
        monthStart.set(Calendar.MINUTE, 0);
        monthStart.set(Calendar.SECOND, 0);
        monthStart.set(Calendar.MILLISECOND, 0);
        
        QueryWrapper<WorkOrder> monthWrapper = baseWrapper.clone();
        monthWrapper.ge("create_time", monthStart.getTime());
        Long monthNew = workOrderMapper.selectCount(monthWrapper);
        
        // 平均处理时长和及时处理率需要复杂计算，这里先返回模拟数据
        Double avgProcessTime = 24.5; // 小时
        Double timelyRate = 85.6; // 百分比
        Double satisfactionScore = 4.2; // 5分制
        
        return new EventOverviewStatisticsResponse(
            total, pending, processing, completed, terminated,
            todayNew, weekNew, monthNew, avgProcessTime, timelyRate, satisfactionScore
        );
    }

    @Override
    public void batchUpdateCoordinates(IModel model, String tenantId) {
        // 批量更新坐标的逻辑
        // 可以根据地址批量获取坐标并更新
        log.info("批量更新坐标功能待实现");
    }

    @Override
    public String getCoordinateByAddress(String address) {
        // 调用地理编码服务获取坐标
        // 这里返回模拟坐标，实际应该调用高德地图等服务
        if (StringUtils.isEmpty(address)) {
            return null;
        }
        
        // 模拟坐标数据
        Random random = new Random();
        double longitude = 116.3 + random.nextDouble() * 0.4; // 北京经度范围
        double latitude = 39.8 + random.nextDouble() * 0.4;   // 北京纬度范围
        
        return String.format("%.6f,%.6f", longitude, latitude);
    }

    @Override
    public IPage<WorkOrder> searchEventsByKeyword(String keyword, String tenantId) {
        EventOverviewPageRequest request = new EventOverviewPageRequest();
        request.setKeyword(keyword);
        request.setTenantId(tenantId);
        request.setPage(1);
        request.setSize(50);
        
        return findEventOverviewByPage(request);
    }

    @Override
    public Object getHotspotStatistics(Long fromTime, Long toTime, String tenantId) {
        // 热点地区统计逻辑
        // 根据地址或坐标统计事件密集区域
        Map<String, Object> result = new HashMap<>();
        result.put("message", "热点地区统计功能待实现");
        return result;
    }
}
