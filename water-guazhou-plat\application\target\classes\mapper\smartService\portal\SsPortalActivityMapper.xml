<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalActivityMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        title,
        image,
        content,
        link,
        active,
        tenant_id
        <!--@sql from ss_portal_activity -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalActivity">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="image" property="image"/>
        <result column="content" property="content"/>
        <result column="link" property="link"/>
        <result column="active" property="active"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_activity
        <where>
            <if test="title != null and title != ''">
                and title like '%' || #{title} || '%'
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="updateFully">
        update ss_portal_activity
        set title   = #{title},
            image   = #{image},
            content = #{content},
            link    = #{link}
        where id = #{id}
    </update>

    <update id="active">
        update ss_portal_activity
        set active = #{active}
        where id = #{id}
    </update>
</mapper>