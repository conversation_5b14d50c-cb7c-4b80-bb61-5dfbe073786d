<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.project.SoBiddingCompanyMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        name,
        bidding_id,
        contact_user,
        phone,
        creator,
        create_time,
        tenant_id<!--@sql from so_bidding_company -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.project.SoBiddingCompany">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="bidding_id" property="biddingId"/>
        <result column="contact_user" property="contactUser"/>
        <result column="phone" property="phone"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_bidding_company
        <where>
            <if test="projectCode != null and projectCode != ''">
                and bidding_id =
                    (select id from so_bidding where project_code = #{projectCode} and tenant_id = #{tenantId})
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <delete id="removeAllByBiddingId">
        delete
        from so_bidding_company
        where bidding_id = #{id}
    </delete>


    <insert id="saveAll">
        INSERT INTO so_bidding_company(id,
                                       name,
                                       bidding_id,
                                       contact_user,
                                       phone,
                                       creator,
                                       create_time,
                                       tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.name},
             #{element.biddingId},
             #{element.contactUser},
             #{element.phone},
             #{element.creator},
             #{element.createTime},
             #{element.tenantId})
        </foreach>
    </insert>
</mapper>