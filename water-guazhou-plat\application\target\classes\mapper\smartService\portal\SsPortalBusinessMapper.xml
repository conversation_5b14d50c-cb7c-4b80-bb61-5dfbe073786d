<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.portal.SsPortalBusinessMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        title,
        introduce,
        cover,
        jump_to_url,
        link,
        content,
        active,
        order_num,
        create_time,
        tenant_id
        <!--@sql from ss_portal_business -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.portal.SsPortalBusiness">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="introduce" property="introduce"/>
        <result column="cover" property="cover"/>
        <result column="jump_to_url" property="jumpToUrl"/>
        <result column="link" property="link"/>
        <result column="content" property="content"/>
        <result column="active" property="active"/>
        <result column="order_num" property="orderNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ss_portal_business
        <where>
            <if test="title != null and title != ''">
                and title like '%' || #{title} || '%'
            </if>
            <if test="active != null">
                and active = #{active}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>

    <update id="updateFully">
        update ss_portal_business
        set title = #{title},
        introduce = #{introduce},
        cover = #{cover},
        link = #{link},
        order_num = #{orderNumber}
        where id = #{id}
    </update>

    <update id="active">
        update ss_portal_business
        set active = #{active}
        where id = #{id}
    </update>
</mapper>