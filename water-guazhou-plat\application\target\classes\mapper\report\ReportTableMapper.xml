<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportTableMapper">

    <update id="update">
        update tb_report_table set pid = #{param.pid}, content = #{param.content}, create_time = #{param.createTime}, tenant_id = #{param.tenantId}
        where id = #{param.id}
    </update>
</mapper>