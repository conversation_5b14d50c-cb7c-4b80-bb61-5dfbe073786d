package org.thingsboard.server.dao.notice;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.notice.NoticeDomain;
import org.thingsboard.server.dao.sql.notice.NoticeMapper;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
@Transactional
public class NoticeServiceImpl implements NoticeService{
    @Autowired
    private NoticeMapper noticeMapper;
    @Override
    public boolean saveNotice(NoticeDomain notice) {
        return noticeMapper.savaNotice(notice);
    }

    @Override
    public boolean delete(String id) {
        return  noticeMapper.deleteNotice(id);
    }

    @Override
    public NoticeDomain getNoticeById(String id) {
        return noticeMapper.getNoticeByid(id);
    }

    @Override
    public PageData getList(String type, Date beginTime, Date endTime, int page, int size) {
        List<NoticeDomain> noticeList = noticeMapper.getList(type, beginTime, endTime, page, size);

        // int total = noticeMapper.getListCount(type, beginTime, endTime);

        return new PageData(noticeList);
    }

    @Override
    public boolean upNotice(NoticeDomain notice) {
        return noticeMapper.updateNotice(notice);
    }



}
