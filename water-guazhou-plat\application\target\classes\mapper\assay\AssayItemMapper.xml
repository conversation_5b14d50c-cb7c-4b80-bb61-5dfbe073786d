<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayItemMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.assay.AssayItem">
        SELECT
        A.*, B.name as settingName, c.first_name as createUserName
        FROM
        tb_assay_item A
        left join tb_assay_base_setting B on A.setting_id = b.id
        left join tb_user c on a.create_user = c.id
        <where>
            <if test="param.settingId != null and param.settingId != ''">
                AND a.setting_id = #{param.settingId}
            </if>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.title != null and param.title != ''">
                AND a.title LIKE '%' || #{param.title} ||'%'
            </if>
        </where>
        ORDER BY a.order_number DESC, a.create_time DESC
    </select>


</mapper>