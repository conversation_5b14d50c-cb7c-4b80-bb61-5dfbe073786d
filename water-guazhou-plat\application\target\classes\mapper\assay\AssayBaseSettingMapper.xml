<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayBaseSettingMapper">

    <select id="findList" resultType="org.thingsboard.server.dao.model.sql.assay.AssayBaseSetting">
        SELECT
        A.*, b.first_name as createUserName
        FROM
        tb_assay_base_setting A
        left join tb_user B on a.create_user = b.id
        <where>
            <if test="param.type != null and param.type != ''">
                AND a.type = #{param.type}
            </if>
            <if test="param.tenantId != null and param.tenantId != ''">
                AND a.tenant_id = #{param.tenantId}
            </if>
            <if test="param.name != null and param.name != ''">
                AND a.name LIKE '%' || #{param.name} ||'%'
            </if>
        </where>
        ORDER BY a.order_number ASC, a.create_time DESC
    </select>


</mapper>