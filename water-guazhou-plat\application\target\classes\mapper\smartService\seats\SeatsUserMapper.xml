<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartService.seats.SeatsUserMapper">
    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartService.seats.SeatsUser">
        select a.*, b.first_name as userName, r.name as roleName, c.name as departmentName, b.phone as phone, c.id as departmentId
        from tb_service_seats_user a
        left join tb_user b on a.user_id = b.id
        left join customer_user_role ur on a.user_id = ur.user_id
        left join customer_role r on r.id = ur.role_id
        left join tb_department c on b.department_id = c.id
        order by a.order_num, a.create_time desc
        offset (#{page} - 1) * #{size}  limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_service_seats_user a
    </select>
    <select id="getUserNo" resultType="java.lang.Integer">
        select max(user_no) from tb_service_seats_user
    </select>
    <select id="selectSeatsIdList" resultType="java.util.Map">
        select a.user_id as seats_id, b.first_name as seats_name
        from tb_service_seats_user a
        left join tb_user b on a.user_id = b.id
        where a.user_id like '%' || #{seatsId} || '%' and a.tenant_id = #{tenantId}
    </select>
</mapper>