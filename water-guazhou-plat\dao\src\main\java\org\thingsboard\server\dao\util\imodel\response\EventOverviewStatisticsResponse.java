package org.thingsboard.server.dao.util.imodel.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 事件总览统计响应
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventOverviewStatisticsResponse {

    /**
     * 总事件数
     */
    private Long total;

    /**
     * 待处理事件数
     */
    private Long pending;

    /**
     * 处理中事件数
     */
    private Long processing;

    /**
     * 已完成事件数
     */
    private Long completed;

    /**
     * 已终止事件数
     */
    private Long terminated;

    /**
     * 今日新增事件数
     */
    private Long todayNew;

    /**
     * 本周新增事件数
     */
    private Long weekNew;

    /**
     * 本月新增事件数
     */
    private Long monthNew;

    /**
     * 平均处理时长（小时）
     */
    private Double avgProcessTime;

    /**
     * 及时处理率（%）
     */
    private Double timelyRate;

    /**
     * 满意度评分
     */
    private Double satisfactionScore;
}
