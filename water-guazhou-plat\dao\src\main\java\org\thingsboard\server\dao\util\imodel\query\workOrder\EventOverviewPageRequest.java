package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.query.PageRequest;

/**
 * 事件总览分页查询请求
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class EventOverviewPageRequest extends PageRequest {

    /**
     * 开始时间（时间戳）
     */
    private Long fromTime;

    /**
     * 结束时间（时间戳）
     */
    private Long toTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 创建人ID
     */
    private String organizerId;

    /**
     * 事件状态
     */
    private String status;

    /**
     * 事件标题（模糊查询）
     */
    private String title;

    /**
     * 地址（模糊查询）
     */
    private String address;

    /**
     * 事件类型
     */
    private String type;

    /**
     * 紧急程度
     */
    private String level;

    /**
     * 处理人ID
     */
    private String processUserId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否只查询有坐标的事件
     */
    private Boolean hasCoordinate;

    /**
     * 关键词搜索（标题、地址、备注）
     */
    private String keyword;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向
     */
    private String orderDirection = "DESC";
}
