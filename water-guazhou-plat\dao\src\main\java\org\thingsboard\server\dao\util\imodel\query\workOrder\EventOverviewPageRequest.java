package org.thingsboard.server.dao.util.imodel.query.workOrder;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.workOrder.EventOverview;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderStatus;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;
import org.thingsboard.server.dao.util.imodel.query.AwareTenantUUID;
import org.thingsboard.server.dao.util.imodel.query.IStarHttpRequest;

import java.util.Date;
import java.util.EnumSet;

/**
 * 事件总览分页查询请求
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class EventOverviewPageRequest extends AdvancedPageableQueryEntity<EventOverview, EventOverviewPageRequest> implements AwareTenantUUID {

    /**
     * 关键词搜索（工单编号、标题、地址）
     */
    private String keyword;

    /**
     * 事件状态
     */
    private String status;

    /**
     * 来源
     */
    private String source;

    /**
     * 紧急程度
     */
    private String level;

    /**
     * 工单类型
     */
    private String type;

    /**
     * 发起人ID
     */
    private String organizerId;

    /**
     * 处理人ID
     */
    private String processUserId;

    /**
     * 当前步骤处理人ID
     */
    private String stepProcessUserId;

    /**
     * 工单编号
     */
    private String serialNo;

    /**
     * 事件标题
     */
    private String title;

    /**
     * 地址
     */
    private String address;

    /**
     * 上报地址
     */
    private String uploadAddress;

    /**
     * 事件描述
     */
    private String remark;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 接收部门ID
     */
    private String receiveDepartmentId;

    /**
     * 是否只查询有坐标的事件
     */
    private Boolean hasCoordinate;

    /**
     * 是否只查询超时事件
     */
    private Boolean isOverdue;

    /**
     * 状态区间查询（如：PENDING,ASSIGN）
     */
    private String statusRange;

    /**
     * 创建时间开始
     */
    private Date createTimeFrom;

    /**
     * 创建时间结束
     */
    private Date createTimeTo;

    /**
     * 完成时间开始
     */
    private Date completeTimeFrom;

    /**
     * 完成时间结束
     */
    private Date completeTimeTo;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向
     */
    private String orderDirection = "DESC";

    /**
     * 获取状态区间枚举集合
     */
    public EnumSet<WorkOrderStatus> getStatusRangeSet() {
        if (statusRange == null || statusRange.trim().isEmpty()) {
            return null;
        }
        
        try {
            String[] statuses = statusRange.split(",");
            if (statuses.length == 1) {
                WorkOrderStatus status = WorkOrderStatus.valueOf(statuses[0].trim());
                return EnumSet.of(status);
            } else if (statuses.length == 2) {
                WorkOrderStatus start = WorkOrderStatus.valueOf(statuses[0].trim());
                WorkOrderStatus end = WorkOrderStatus.valueOf(statuses[1].trim());
                return EnumSet.range(start, end);
            }
        } catch (IllegalArgumentException e) {
            // 忽略无效的状态值
        }
        
        return null;
    }

    /**
     * 获取单个状态枚举
     */
    public WorkOrderStatus getStatusEnum() {
        if (status == null || status.trim().isEmpty()) {
            return null;
        }
        
        try {
            return WorkOrderStatus.valueOf(status.trim());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @Override
    public String valid(IStarHttpRequest request) {
        // 验证状态参数
        if (status != null && !status.trim().isEmpty()) {
            try {
                WorkOrderStatus.valueOf(status.trim());
            } catch (IllegalArgumentException e) {
                return "无效的事件状态: " + status;
            }
        }

        // 验证状态区间参数
        if (statusRange != null && !statusRange.trim().isEmpty()) {
            String[] statuses = statusRange.split(",");
            if (statuses.length > 2) {
                return "状态区间参数最多包含两个状态";
            }
            
            for (String s : statuses) {
                try {
                    WorkOrderStatus.valueOf(s.trim());
                } catch (IllegalArgumentException e) {
                    return "无效的状态区间参数: " + s;
                }
            }
            
            if (statuses.length == 2) {
                try {
                    WorkOrderStatus start = WorkOrderStatus.valueOf(statuses[0].trim());
                    WorkOrderStatus end = WorkOrderStatus.valueOf(statuses[1].trim());
                    if (start.ordinal() > end.ordinal()) {
                        return "状态区间起始状态不能大于结束状态";
                    }
                } catch (IllegalArgumentException e) {
                    return "状态区间参数格式错误";
                }
            }
        }

        // 验证时间参数
        if (createTimeFrom != null && createTimeTo != null && createTimeFrom.after(createTimeTo)) {
            return "创建时间开始不能晚于结束时间";
        }

        if (completeTimeFrom != null && completeTimeTo != null && completeTimeFrom.after(completeTimeTo)) {
            return "完成时间开始不能晚于结束时间";
        }

        // 验证排序参数
        if (orderBy != null && !orderBy.trim().isEmpty()) {
            String[] validOrderFields = {
                "create_time", "update_time", "complete_time", "estimated_finish_time",
                "serial_no", "title", "level", "status", "priority_score"
            };
            
            boolean validField = false;
            for (String field : validOrderFields) {
                if (field.equals(orderBy.trim())) {
                    validField = true;
                    break;
                }
            }
            
            if (!validField) {
                return "无效的排序字段: " + orderBy;
            }
        }

        if (orderDirection != null && !orderDirection.trim().isEmpty()) {
            String direction = orderDirection.trim().toUpperCase();
            if (!"ASC".equals(direction) && !"DESC".equals(direction)) {
                return "排序方向只能是ASC或DESC";
            }
        }

        return null;
    }

    @Override
    public void tenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String tenantId() {
        return tenantId;
    }
}
