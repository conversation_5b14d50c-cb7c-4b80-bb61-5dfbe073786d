<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayReportDataAddressMapper">

    <insert id="batchInsert">
        INSERT INTO tb_assay_report_data_address(id, pid, create_time, status, tenant_id, value, address) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.pid},
            #{element.createTime},
            #{element.status},
            #{element.tenantId},
            #{element.value},
            #{element.address}
            )
        </foreach>
    </insert>

    <select id="getListByPidIn" resultType="org.thingsboard.server.dao.model.sql.assay.AssayReportDataAddress">
        select * from tb_assay_report_data_address where pid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by create_time
    </select>

</mapper>