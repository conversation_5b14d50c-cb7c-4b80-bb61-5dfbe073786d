<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionEstimateMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        estimate.id,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        estimate.budgeter,
        estimate.cost,
        estimate.address,
        info.status,
        estimate.remark,
        estimate.attachments,
        estimate.creator,
        estimate.create_time,
        estimate.update_user,
        estimate.update_time,
        construction.tenant_id
        <!--@sql from so_construction_estimate estimate, so_construction construction, so_construction_task_info info -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionEstimate">
        <result column="id" property="id"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>
        <result column="budgeter" property="budgeter"/>
        <result column="cost" property="cost"/>
        <result column="address" property="address"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_ESTIMATE"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_estimate estimate
                           on estimate.construction_code = construction.code and
                              estimate.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = estimate.construction_code and
                              info.tenant_id = estimate.tenant_id
                               and info.scope = #{scope}
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="fromTime != null">
                and estimate.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and estimate.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
        order by construction.create_time desc
    </select>

    <update id="update">
        update so_construction_estimate
        <set>
            <if test="budgeter != null">
                budgeter = #{budgeter},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_estimate
        set budgeter    = #{budgeter},
            cost        = #{cost},
            address     = #{address},
            remark      = #{remark},
            attachments = #{attachments}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_construction_estimate(id,
                                             construction_code,
                                             budgeter,
                                             cost,
                                             address,
                                             remark,
                                             attachments,
                                             creator,
                                             create_time,
                                             update_user,
                                             update_time,
                                             tenant_id)
        VALUES (#{id},
                #{constructionCode},
                #{budgeter},
                #{cost},
                #{address},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_estimate
        where id = #{id}
    </select>

    <select id="getIdByConstructionCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_construction_estimate
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>