<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.purchase.DevicePurchaseMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           code,
                           title,
                           user_id,
                           use_way,
                           upload_time,
                           pre_time,
                           budget,
                           add_record,
                           creator,
                           create_time,
                           tenant_id<!--@sql from device_purchase -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.purchase.DevicePurchase">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="title" property="title"/>
        <result column="user_id" property="userId"/>
        <result column="use_way" property="useWay"/>
        <result column="upload_time" property="uploadTime"/>
        <result column="pre_time" property="preTime"/>
        <result column="budget" property="budget"/>
        <result column="add_record" property="addRecord"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from device_purchase
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="code != null and code != ''">
                and code like '%' || #{code} || '%'
            </if>
            <if test="title != null and title != ''">
                and title like '%' || #{title} || '%'
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="(userId == null or userId == '') and userDepartmentId != null and userDepartmentId != ''">
                and is_user_at_department(device_purchase.user_id, #{userDepartmentId})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update device_purchase
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="useWay != null">
                use_way = #{useWay},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
            <if test="preTime != null">
                pre_time = #{preTime},
            </if>
            <if test="budget != null">
                budget = #{budget},
            </if>
            <if test="addRecord != null">
                add_record = #{addRecord},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getNameById" resultType="java.lang.String" useCache="true">
        select title
        from device_purchase
        where id = #{id}
    </select>
</mapper>