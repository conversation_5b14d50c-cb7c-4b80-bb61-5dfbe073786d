<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.dispatch.EmergencyUserMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           phone,
                           dept_id,
                           department_get_name(dept_id) dept_name,
                           creator,
                           create_time,
                           tenant_id<!--@sql from sp_emergency_user -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartProduction.dispatch.EmergencyUser">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_emergency_user
        <where>
            <if test="name != null and name != ''">
                and str_like(name, #{name})
            </if>
            <if test="deptId != null and deptId != ''">
                and department_at_department(dept_id, #{deptId})
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time desc
    </select>

    <update id="update">
        update sp_emergency_user
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="clear">
        delete
        from sp_emergency_user
        where tenant_id = #{tenantId}
    </delete>

    <insert id="saveAll">
        INSERT INTO sp_emergency_user(id,
                                      name,
                                      phone,
                                      dept_id,
                                      creator,
                                      create_time,
                                      tenant_id)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.name},
             #{element.phone},
             #{element.deptId},
             #{creator},
             #{createTime},
             #{tenantId})
        </foreach>
    </insert>

    <select id="selectFromUserEntity" resultMap="BaseResultMap">
        select first_name    "name",
               phone,
               department_id dept_id
        from tb_user
        where tenant_id = #{tenantId}
          and id not in (select i.id from sp_emergency_user i)
          and (first_name, phone) not in (select i.name, i.phone from sp_emergency_user i)
    </select>

    <select id="findByDepartmentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sp_emergency_user
        where dept_id = #{departmentId}
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select name from sp_emergency_user where id = #{id}
    </select>
</mapper>