<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.dma.OldDmaPartitionMapper">

    <select id="getRootIdNameMapList" resultType="java.util.Map">
        select a.id, a.name from dma_partition a where (a.pid is null or a.pid = '') and a.tenant_id = #{tenantId} order by order_num, a.create_time desc
    </select>

    <select id="getRootIdNameList" resultType="org.thingsboard.server.dao.model.sql.dma.OldDmaPartitionEntity">
        select a.id, a.name from dma_partition a where (a.pid is null or a.pid = '') and a.tenant_id = #{tenantId} order by order_num, a.create_time desc
    </select>

    <select id="getAllIdNameByPid" resultType="org.thingsboard.server.dao.model.sql.dma.OldDmaPartitionEntity">
        select a.id, a.name from dma_partition a where a.pid = #{pid} order by order_num, a.create_time desc
    </select>

    <select id="getIdNameById" resultType="java.util.Map">
        select a.id, a.name from dma_partition a where a.id = #{id} order by order_num, a.create_time desc
    </select>
</mapper>