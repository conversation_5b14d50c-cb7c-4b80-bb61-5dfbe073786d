<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipeMinFlowConfigMapper">

    <select id="getListByPartitionIdIn" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PipeMinFlowConfig">
        select a.*, b.id as partitionId, b.name as partitionName, b.copy_meter_type as copyMeterType, b.main_line_length as mainLineLength
        from tb_pipe_partition b
        left join tb_pipe_min_flow_config a on b.id = a.partition_id
        where b.id in
        <foreach collection="partitionIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        order by b.create_time desc
    </select>

</mapper>