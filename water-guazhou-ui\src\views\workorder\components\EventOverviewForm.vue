<!-- 事件总览表单 -->
<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑事件' : '新增事件'"
    width="1000px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="event-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="请选择事件类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件名称" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入事件名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="formData.createTime"
              type="datetime"
              placeholder="请选择创建时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="紧急程度" prop="level">
            <el-select
              v-model="formData.level"
              placeholder="请选择紧急程度"
              style="width: 100%"
            >
              <el-option label="低" value="LOW" />
              <el-option label="中" value="MEDIUM" />
              <el-option label="高" value="HIGH" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input
              v-model="formData.address"
              placeholder="请输入地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 地图选择区域 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="选择位置">
            <div class="map-selector">
              <div class="map-container">
                <FormMap
                  v-model="mapLocation"
                  :show-input="true"
                  :handle-inverse-geocodeing="handleInverseGeocodeing"
                  style="height: 350px"
                  @change="handleMapLocationChange"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FormMap from '@/components/arcMap/FormMap.vue'
import { InverseGeocoding } from '@/api/mapservice/utils'
import request from '@/plugins/axios'

interface EventFormData {
  id?: string
  type: string
  title: string
  createTime: string
  level: string
  address: string
  remark: string
  coordinate?: string
}

const props = defineProps<{
  modelValue: boolean
  isEdit?: boolean
  editData?: EventFormData
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: EventFormData]
}>()

const visible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const typeOptions = ref<Array<{label: string, value: string}>>([])

// 地图相关
const mapLocation = ref<number[]>([98.4842, 40.1677]) // 默认瓜州县中心坐标

// 表单数据
const formData = reactive<EventFormData>({
  type: '',
  title: '',
  createTime: '',
  level: '',
  address: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  type: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
  title: [{ required: true, message: '请输入事件名称', trigger: 'blur' }],
  createTime: [{ required: true, message: '请选择创建时间', trigger: 'change' }],
  address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    initForm()
    loadTypeOptions()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 初始化表单
const initForm = () => {
  if (props.isEdit && props.editData) {
    Object.assign(formData, props.editData)
    if (props.editData.coordinate) {
      const coords = props.editData.coordinate.split(',')
      mapLocation.value = [parseFloat(coords[0]), parseFloat(coords[1])]
    }
  } else {
    // 新增时设置默认值
    Object.assign(formData, {
      type: '',
      title: '',
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      level: '',
      address: '',
      remark: ''
    })
    mapLocation.value = [98.4842, 40.1677] // 重置为默认坐标
  }
}

// 加载事件类型选项
const loadTypeOptions = async () => {
  try {
    const res = await request({
      url: '/api/workOrderType/list',
      method: 'get',
      params: { status: 1 }
    })
    
    if (res.data?.data) {
      // 只获取父级类型
      const parentTypes = res.data.data.filter((item: any) => !item.parentId)
      typeOptions.value = parentTypes.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取事件类型失败:', error)
  }
}



// FormMap组件的逆地理编码处理
const handleInverseGeocodeing = (res: any) => {
  if (res.data?.result?.formatted_address) {
    formData.address = res.data.result.formatted_address
  }
}

// 地图位置变化处理
const handleMapLocationChange = (location: number[]) => {
  if (location && location.length === 2) {
    formData.coordinate = `${location[0]},${location[1]}`
    ElMessage.success('位置选择成功')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    const submitData = { ...formData }
    // 如果选择了地图位置，添加坐标信息
    if (mapLocation.value && mapLocation.value.length === 2) {
      submitData.coordinate = `${mapLocation.value[0]},${mapLocation.value[1]}`
    }

    emit('submit', submitData)

  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
  mapLocation.value = [98.4842, 40.1677] // 重置为默认坐标
}
</script>

<style lang="scss" scoped>
.event-form {
  .address-input-group {
    display: flex;
    align-items: center;
  }

  .map-selector {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;

    .map-selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #dcdfe6;

      span {
        font-size: 14px;
        color: #606266;
      }
    }

    .map-container {
      height: 350px;
      width: 100%;

      :deep(.location-map) {
        width: 100%;
        height: 100%;
      }

      :deep(.locate-search-box) {
        width: 100%;
      }
    }

    .coordinate-info {
      padding: 8px 16px;
      background-color: #f0f9ff;
      border-top: 1px solid #dcdfe6;
      font-size: 12px;
      color: #409eff;
    }
  }
}

// 确保表单项的宽度一致
:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
