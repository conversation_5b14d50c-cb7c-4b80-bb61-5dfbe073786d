<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PartitionPumpHouseMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.smartPipe.dma.PartitionPumpHouse">
        select a.*
        from tb_pipe_partition_pump_house a
        <where>
            <if test="param.partitionId != null and param.partitionId != ''">
                and a.partition_id = #{param.partitionId}
            </if>
            <if test="param.brand != null and param.brand != ''">
                and a.brand like '%' || #{param.brand} || '%'
            </if>
            <if test="param.caliber != null and param.caliber != ''">
                and a.caliber like '%' || #{param.caliber} || '%'
            </if>
            and a.tenant_id = #{param.tenantId}
            order by a.create_time desc
        </where>

    </select>
</mapper>