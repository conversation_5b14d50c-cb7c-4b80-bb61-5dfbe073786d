<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionDesignMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->design.id,
                           design.code,
                           construction.code                                                  as construction_code,
                           construction.name                                                  as construction_name,
                           construction.type_id                                               as construction_type_id,
                           (select name from so_general_type where id = construction.type_id) as construction_type_name,
                           design.type,
                           design.cost,
                           design.pip_length_design,
                           info.status,
                           design.remark,
                           design.attachments,
                           design.creator,
                           design.create_time,
                           design.update_user,
                           design.update_time,
        construction.tenant_id<!--@sql from so_construction_design design, so_construction construction, so_construction_task_info info -->
    </sql>

    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionDesign">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>
        <result column="type" property="type"/>
        <result column="cost" property="cost"/>
        <result column="pip_length_design" property="pipLengthDesign"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_DESIGN"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_design design
                           on design.construction_code = construction.code and design.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id and
                              info.scope = #{scope}
                               and info.scope = #{scope}
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%'|| #{constructionName} ||'%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="fromTime != null">
                and design.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and design.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
        order by construction.create_time desc
    </select>

    <update id="update">
        update so_construction_design
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="pipLengthDesign != null">
                pip_length_design = #{pipLengthDesign},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_user = #{updateUser},
            update_time = #{updateTime}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_design
        set type              = #{type},
            cost              = #{cost},
            pip_length_design = #{pipLengthDesign},
            remark            = #{remark},
            attachments       = #{attachments},
            update_user       = #{updateUser},
            update_time       = #{updateTime}
        where id = #{id}
    </update>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_design
        where id = #{id}
    </select>
</mapper>