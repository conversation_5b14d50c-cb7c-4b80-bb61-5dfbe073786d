<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionExpenseMapper">
    <!--suppress SqlShadowingAlias -->
    <sql id="Base_Column_List">
        <!--@sql select -->
        expense.id,
        expense.code,
        construction.code as construction_code,
        construction.name as construction_name,
        expense.contract_code,
        <!--@formatter:off-->
        (select name from so_construction_contract contract
        where code = contract_code and tenant_id = expense.tenant_id
            and construction_code = info.construction_code)    as contract_name,

        (select type from so_construction_contract contract
        where code = contract_code and tenant_id = expense.tenant_id
             and construction_code = info.construction_code)    as contract_type,

        (select name from so_general_type where id = (select type from so_construction_contract contract
        where code = contract_code and tenant_id = expense.tenant_id
            and construction_code = info.construction_code))    as contract_type_name,

        (select sum(cost) from so_construction_contract contract
        where code = contract_code and tenant_id = expense.tenant_id
              and construction_code = info.construction_code)            as contract_cost,
        <!--@formatter:on-->
        expense.type,
        expense.cost,
        expense.payment_type,
        expense.approval_time,
        expense.submit_finance_time,
        expense.first_verify_cost,
        expense.first_verify_organization,
        expense.second_verify_cost,
        expense.second_verify_organization,
        expense.payee_info,
        expense.payee_organization,
        contract_total_cost,
        expense_total_cost,
        settlement_total_cost,
        expense.remark,
        expense.attachments,
        info.status as status,
        expense.creator,
        expense.create_time,
        expense.update_user,
        expense.update_time,
        construction.tenant_id
        <!--@sql from so_construction_expense expense, so_construction construction, so_construction_task_info info, so_construction_details details -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpense">
        <result column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="contract_code" property="contractCode"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_type" property="contractType"/>
        <result column="contract_type_name" property="contractTypeName"/>
        <result column="contract_cost" property="contractCost"/>
        <result column="type" property="type"/>
        <result column="cost" property="cost"/>
        <result column="payment_type" property="paymentType"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="submit_finance_time" property="submitFinanceTime"/>
        <result column="first_verify_cost" property="firstVerifyCost"/>
        <result column="first_verify_organization" property="firstVerifyOrganization"/>
        <result column="second_verify_cost" property="secondVerifyCost"/>
        <result column="second_verify_organization" property="secondVerifyOrganization"/>
        <result column="payee_info" property="payeeInfo"/>
        <result column="payee_organization" property="payeeOrganization"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="SoConstructionExpenseContainerResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionExpenseContainer">
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="contract_total_cost" property="contractTotalCost"/>
        <result column="settlement_total_cost" property="finalReportCost"/>
        <result column="expense_total_cost" property="totalCost"/>
        <result column="status" property="status"/>
        <collection property="items" resultMap="BaseResultMap"/>
    </resultMap>

    <select id="findByPage" resultMap="SoConstructionExpenseContainerResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_EXPENSE"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_expense expense
                           on expense.construction_code = construction.code and
                              expense.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
                 left join so_construction_details details
                           on construction.code = details.current_construction_code and
                              construction.tenant_id = details.current_tenant_id
        where construction.code in (
        <include refid="pageInfoSubQuery"/>
        <if test="size > 0">
            offset #{offset} limit #{size}
        </if>
        )
          and construction.tenant_id = #{tenantId}
        order by construction.create_time desc
    </select>

    <select id="countByPage" resultType="long">
        select count(1) from (<include refid="pageInfoSubQuery"/>) a
    </select>

    <sql id="pageInfoSubQuery">
        select distinct construction.code
        from so_construction construction
                 left join so_construction_expense expense
                           on expense.construction_code = construction.code and
                              expense.tenant_id = construction.tenant_id
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
    </sql>

    <update id="update">
        update so_construction_expense
        <set>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="contractCode != null">
                contract_code = #{contractCode},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="paymentType != null">
                payment_type = #{paymentType},
            </if>
            <if test="approvalTime != null">
                approval_time = #{approvalTime},
            </if>
            <if test="submitFinanceTime != null">
                submit_finance_time = #{submitFinanceTime},
            </if>
            <if test="firstVerifyCost != null">
                first_verify_cost = #{firstVerifyCost},
            </if>
            <if test="firstVerifyOrganization != null">
                first_verify_organization = #{firstVerifyOrganization},
            </if>
            <if test="secondVerifyCost != null">
                second_verify_cost = #{secondVerifyCost},
            </if>
            <if test="secondVerifyOrganization != null">
                second_verify_organization = #{secondVerifyOrganization},
            </if>
            <if test="payeeInfo != null">
                payee_info = #{payeeInfo},
            </if>
            <if test="payeeOrganization != null">
                payee_organization = #{payeeOrganization},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_user = #{updateUser},
            update_time = #{updateTime}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_expense
        set contract_code              = #{contractCode},
            type                       = #{type},
            cost                       = #{cost},
            payment_type               = #{paymentType},
            approval_time              = #{approvalTime},
            submit_finance_time        = #{submitFinanceTime},
            first_verify_cost          = #{firstVerifyCost},
            first_verify_organization  = #{firstVerifyOrganization},
            second_verify_cost         = #{secondVerifyCost},
            second_verify_organization = #{secondVerifyOrganization},
            payee_info                 = #{payeeInfo},
            payee_organization         = #{payeeOrganization},
            remark                     = #{remark},
            attachments                = #{attachments},
            update_user                = #{updateUser},
            update_time                = #{updateTime}
        where id = #{id}
    </update>

    <select id="isCodeExists" resultType="boolean">
        select count(1)
        from so_construction outside
        where outside.code = #{code}
          and tenant_id = #{tenantId}
        <if test="id != null and id != ''">
            <!--需要存在，下方不存在是会返回null，null意味着false(不重复)，会出现假是自己(假不重复)-->
            and (select count(1) > 0 from so_construction_expense where id = #{id})
            <!--两个code相等则意味着是自己，不是自己则重复，是自己则放行(false-是自己所以返回未重复，true-不是自己所以返回重复)-->
            and (select not outside.code = code from so_construction_expense where id = #{id})
        </if>
    </select>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_expense
        where id = #{id}
    </select>
</mapper>