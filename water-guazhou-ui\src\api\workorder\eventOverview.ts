import { request } from '@/utils/request'

// 事件总览分页查询参数
export interface EventOverviewPageRequest {
  page: number
  size: number
  fromTime?: number
  toTime?: number
  source?: string
  organizerId?: string
  status?: string
  title?: string
  address?: string
}

// 事件总览响应数据
export interface EventOverviewItem {
  id: string
  serialNo: string
  title: string
  source: string
  sourceName: string
  organizerId: string
  organizerIdName: string
  address: string
  uploadAddress: string
  coordinate?: string // 坐标信息，格式：经度,纬度
  status: string
  statusName: string
  level: string
  levelName: string
  type: string
  typeName: string
  createTime: string
  updateTime: string
  remark: string
  processUserId: string
  processUserIdName: string
  stepProcessUserId: string
  stepProcessUserIdName: string
}

// 事件总览分页响应
export interface EventOverviewPageResponse {
  data: EventOverviewItem[]
  total: number
  page: number
  size: number
}

// 获取事件总览分页数据
export const GetEventOverviewPage = (params: EventOverviewPageRequest) => {
  return request<{
    code: number
    message: string
    data: EventOverviewPageResponse
  }>({
    url: '/api/eventOverview',
    method: 'GET',
    params
  })
}

// 获取事件详情
export const GetEventDetail = (id: string) => {
  return request<{
    code: number
    message: string
    data: EventOverviewItem
  }>({
    url: `/api/eventOverview/${id}`,
    method: 'GET'
  })
}

// 更新事件坐标信息
export const UpdateEventCoordinate = (id: string, coordinate: string) => {
  return request<{
    code: number
    message: string
  }>({
    url: `/api/eventOverview/${id}/coordinate`,
    method: 'PUT',
    data: { coordinate }
  })
}

// 获取事件统计信息
export const GetEventStatistics = (params: {
  fromTime?: number
  toTime?: number
  source?: string
}) => {
  return request<{
    code: number
    message: string
    data: {
      total: number
      pending: number
      processing: number
      completed: number
      terminated: number
    }
  }>({
    url: '/api/eventOverview/statistics',
    method: 'GET',
    params
  })
}
