import request from '@/plugins/axios'

/**
 * 事件总览API接口
 */

/**
 * 事件总览查询参数接口
 */
export interface EventOverviewPageRequest {
  page?: number
  size?: number
  title?: string
  type?: string
  status?: string
  createTimeFrom?: string
  createTimeTo?: string
  orderBy?: string
  orderDirection?: string
}

/**
 * 事件总览实体接口
 */
export interface EventOverview {
  id: string
  workOrderId: string
  title: string
  type: string
  level: string
  address: string
  remark: string
  status: string
  statusName?: string
  organizerId: string
  organizerIdName?: string
  processUserId: string
  processUserIdName?: string
  stepProcessUserId: string
  stepProcessUserIdName?: string
  createTime: string
  updateTime: string
  tenantId: string
  projectId: string
  projectName?: string
  coordinate?: string
  coordinateName?: string
  receiveDepartmentId?: string
  receiveDepartmentName?: string
  ccUserId?: string
  ccUserName?: string
  parentId?: string
}

/**
 * 坐标更新参数接口
 */
export interface CoordinateUpdateRequest {
  coordinate: string
  coordinateName?: string
}

/**
 * 事件创建/更新参数接口
 */
export interface EventOverviewCreateRequest {
  title: string
  type: string
  level: string
  address: string
  remark?: string
  coordinate?: string
  coordinateName?: string
}

export interface EventOverviewUpdateRequest extends EventOverviewCreateRequest {
  id: string
}

/**
 * 分页查询事件总览
 */
export const GetEventOverviewPage = (params: EventOverviewPageRequest) => {
  return request({
    url: '/api/eventOverview',
    method: 'get',
    params
  })
}

/**
 * 根据ID查询事件详情
 */
export const GetEventOverviewById = (id: string) => {
  return request({
    url: `/api/eventOverview/${id}`,
    method: 'get'
  })
}

/**
 * 创建事件
 */
export const CreateEventOverview = (data: EventOverviewCreateRequest) => {
  return request({
    url: '/api/eventOverview',
    method: 'post',
    data
  })
}

/**
 * 更新事件
 */
export const UpdateEventOverview = (data: EventOverviewUpdateRequest) => {
  return request({
    url: `/api/eventOverview/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除事件
 */
export const DeleteEventOverview = (id: string) => {
  return request({
    url: `/api/eventOverview/${id}`,
    method: 'delete'
  })
}

/**
 * 更新事件坐标信息
 */
export const UpdateEventCoordinate = (id: string, data: CoordinateUpdateRequest) => {
  return request({
    url: `/api/eventOverview/${id}/coordinate`,
    method: 'post',
    data
  })
}

/**
 * 批量更新事件坐标信息
 */
export const BatchUpdateEventCoordinates = (updates: Array<{id: string, coordinate: string, coordinateName?: string}>) => {
  return request({
    url: '/api/eventOverview/coordinates/batch',
    method: 'post',
    data: updates
  })
}

/**
 * 获取事件统计信息
 */
export const GetEventStatistics = (projectId?: string) => {
  return request({
    url: '/api/eventOverview/statistics',
    method: 'get',
    params: { projectId }
  })
}

/**
 * 获取事件热点地区统计
 */
export const GetEventHotspotStatistics = (limit: number = 10) => {
  return request({
    url: '/api/eventOverview/hotspots',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取事件趋势统计
 */
export const GetEventTrendStatistics = (days: number = 30) => {
  return request({
    url: '/api/eventOverview/trends',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取事件状态分布统计
 */
export const GetEventStatusDistribution = (projectId?: string) => {
  return request({
    url: '/api/eventOverview/status-distribution',
    method: 'get',
    params: { projectId }
  })
}

/**
 * 获取处理效率统计
 */
export const GetProcessEfficiencyStatistics = (days: number = 30) => {
  return request({
    url: '/api/eventOverview/efficiency',
    method: 'get',
    params: { days }
  })
}

/**
 * 获取指定范围内的事件
 */
export const FindEventsInRadius = (centerCoordinate: string, radiusMeters: number = 1000) => {
  return request({
    url: '/api/eventOverview/nearby',
    method: 'get',
    params: { centerCoordinate, radiusMeters }
  })
}

/**
 * 导出事件数据
 */
export const ExportEventOverview = (params: EventOverviewPageRequest) => {
  return request({
    url: '/api/eventOverview/export',
    method: 'post',
    data: params
  })
}

/**
 * 获取超时事件列表
 */
export const GetOverdueEvents = (limit: number = 20) => {
  return request({
    url: '/api/eventOverview/overdue',
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取高优先级事件列表
 */
export const GetHighPriorityEvents = (limit: number = 20) => {
  return request({
    url: '/api/eventOverview/high-priority',
    method: 'get',
    params: { limit }
  })
}

/**
 * 验证坐标格式
 */
export const ValidateCoordinate = (coordinate: string) => {
  return request({
    url: '/api/eventOverview/validate-coordinate',
    method: 'get',
    params: { coordinate }
  })
}
