package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

/**
 * 事件总览实体
 * 基于工单数据的事件总览视图
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ResponseEntity
@TableName("event_overview")
public class EventOverview {
    
    @TableId
    private String id;
    
    // 工单编号
    private String workOrderId;
    
    // 事件标题
    private String title;
    
    // 来源
    private String source;
    
    // 事件类型
    private String type;
    
    // 紧急程度
    private String level;
    
    // 发生地点
    private String address;

    // 事件描述
    private String remark;
    
    // 事件状态
    @Info(name = "statusName")
    private WorkOrderStatus status;
    
    // 创建人
    @ParseUsername
    private String organizerId;
    
    // 处理人
    @ParseUsername
    private String processUserId;
    
    // 当前步骤处理人
    @ParseUsername
    private String stepProcessUserId;
    
    // 创建时间
    private Date createTime;

    // 更新时间
    private Date updateTime;

    // 租户ID
    private String tenantId;
    
    // 项目ID
    private String projectId;
    
    // 地理坐标（经度,纬度）
    private String coordinate;
    
    // 地理位置名称
    private String coordinateName;

    // 接收部门ID
    private String receiveDepartmentId;
    
    // 抄送人
    private String ccUserId;
    
    // 父工单ID
    private String parentId;
    
    // 扩展字段：项目名称
    @TableField(exist = false)
    private String projectName;
    
    // 扩展字段：接收部门名称
    @TableField(exist = false)
    private String receiveDepartmentName;
    
    // 扩展字段：抄送人姓名
    @TableField(exist = false)
    private String ccUserName;
    
    /**
     * 获取状态名称
     */
    @SuppressWarnings("unused")
    private String statusName() {
        return status != null ? status.getStageName() : "";
    }
    
    /**
     * 获取事件阶段
     */
    public WorkOrderStatus stage() {
        return status;
    }
    
    /**
     * 判断是否有地理坐标
     */
    public boolean hasCoordinate() {
        return coordinate != null && !coordinate.trim().isEmpty() && coordinate.contains(",");
    }
    
    /**
     * 获取经度
     */
    public Double getLongitude() {
        if (!hasCoordinate()) return null;
        try {
            String[] coords = coordinate.split(",");
            return Double.parseDouble(coords[0].trim());
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取纬度
     */
    public Double getLatitude() {
        if (!hasCoordinate()) return null;
        try {
            String[] coords = coordinate.split(",");
            if (coords.length > 1) {
                return Double.parseDouble(coords[1].trim());
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    

}
