package org.thingsboard.server.dao.model.sql.workOrder;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.util.imodel.response.annotations.Info;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

/**
 * 事件总览实体
 * 基于工单数据的事件总览视图
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ResponseEntity
@TableName("work_orders")
public class EventOverview {
    
    @TableId
    private String id;
    
    // 工单编号
    private String serialNo;
    
    // 事件标题
    private String title;
    
    // 来源
    private String source;
    
    // 事件类型
    private String type;
    
    // 紧急程度
    private String level;
    
    // 发生地点
    private String address;
    
    // 上报地点
    private String uploadAddress;
    
    // 事件描述
    private String remark;
    
    // 事件状态
    @Info(name = "statusName")
    private WorkOrderStatus status;
    
    // 创建人
    @ParseUsername
    private String organizerId;
    
    // 处理人
    @ParseUsername
    private String processUserId;
    
    // 当前步骤处理人
    @ParseUsername
    private String stepProcessUserId;
    
    // 创建时间
    private Date createTime;
    
    // 更新时间
    private Date updateTime;
    
    // 完成时间
    private Date completeTime;
    
    // 预计完成时间
    private Date estimatedFinishTime;
    
    // 租户ID
    private String tenantId;
    
    // 项目ID
    private String projectId;
    
    // 地理坐标（经度,纬度）
    private String coordinate;
    
    // 地理位置名称
    private String coordinateName;
    
    // 上报人
    @ParseUsername(withDepartment = true, withOrganization = true)
    private String uploadUserId;
    
    // 上报人电话
    private String uploadPhone;
    
    // 现场图片
    private String imgUrl;
    
    // 现场视频
    private String videoUrl;
    
    // 现场音频
    private String audioUrl;
    
    // 其他附件
    private String otherFileUrl;
    
    // 接收部门ID
    private String receiveDepartmentId;
    
    // 抄送人
    private String ccUserId;
    
    // 父工单ID
    private String parentId;
    
    // 扩展字段：项目名称
    @TableField(exist = false)
    private String projectName;
    
    // 扩展字段：接收部门名称
    @TableField(exist = false)
    private String receiveDepartmentName;
    
    // 扩展字段：抄送人姓名
    @TableField(exist = false)
    private String ccUserName;
    
    // 扩展字段：处理耗时（小时）
    @TableField(exist = false)
    private Double processHours;
    
    // 扩展字段：是否超时
    @TableField(exist = false)
    private Boolean isOverdue;
    
    // 扩展字段：优先级分数（用于排序）
    @TableField(exist = false)
    private Integer priorityScore;
    
    /**
     * 获取状态名称
     */
    @SuppressWarnings("unused")
    private String statusName() {
        return status != null ? status.getStageName() : "";
    }
    
    /**
     * 获取事件阶段
     */
    public WorkOrderStatus stage() {
        return status;
    }
    
    /**
     * 判断是否有地理坐标
     */
    public boolean hasCoordinate() {
        return coordinate != null && !coordinate.trim().isEmpty() && coordinate.contains(",");
    }
    
    /**
     * 获取经度
     */
    public Double getLongitude() {
        if (!hasCoordinate()) return null;
        try {
            String[] coords = coordinate.split(",");
            return Double.parseDouble(coords[0].trim());
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 获取纬度
     */
    public Double getLatitude() {
        if (!hasCoordinate()) return null;
        try {
            String[] coords = coordinate.split(",");
            if (coords.length > 1) {
                return Double.parseDouble(coords[1].trim());
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 计算处理耗时
     */
    public void calculateProcessHours() {
        if (createTime != null && completeTime != null) {
            long diffInMillies = completeTime.getTime() - createTime.getTime();
            this.processHours = diffInMillies / (1000.0 * 60 * 60); // 转换为小时
        }
    }
    
    /**
     * 判断是否超时
     */
    public void checkOverdue() {
        if (estimatedFinishTime != null) {
            Date now = completeTime != null ? completeTime : new Date();
            this.isOverdue = now.after(estimatedFinishTime);
        } else {
            this.isOverdue = false;
        }
    }
    
    /**
     * 计算优先级分数（用于排序）
     */
    public void calculatePriorityScore() {
        int score = 0;
        
        // 紧急程度权重
        if ("高".equals(level) || "HIGH".equals(level)) {
            score += 100;
        } else if ("中".equals(level) || "MEDIUM".equals(level)) {
            score += 50;
        } else if ("低".equals(level) || "LOW".equals(level)) {
            score += 10;
        }
        
        // 状态权重
        if (status != null) {
            switch (status) {
                case PENDING:
                case ASSIGN:
                    score += 80; // 待处理状态优先级高
                    break;
                case RESOLVING:
                case PROCESSING:
                    score += 60; // 处理中状态
                    break;
                case REVIEW:
                case SUBMIT:
                    score += 40; // 审核状态
                    break;
                default:
                    score += 20; // 其他状态
                    break;
            }
        }
        
        // 超时权重
        if (Boolean.TRUE.equals(isOverdue)) {
            score += 200; // 超时事件优先级最高
        }
        
        this.priorityScore = score;
    }
}
