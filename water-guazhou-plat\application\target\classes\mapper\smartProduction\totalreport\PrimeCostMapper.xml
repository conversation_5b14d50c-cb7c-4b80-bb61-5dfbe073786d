<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartProduction.totalreport.PrimeCostMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           factory,
                           product,
                           power_cost,
                           water_amount,
                           medical_amount,
                           medical_price,
                           ym<!--@sql from prime_cost -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCost">
        <result column="id" property="id"/>
        <result column="factory" property="factory"/>
        <result column="product" property="product"/>
        <result column="power_cost" property="powerCost"/>
        <result column="water_amount" property="waterAmount"/>
        <result column="medical_price" property="medicalPrice"/>
        <result column="medical_amount" property="medicalAmount"/>
        <result column="ym" property="ym"/>
    </resultMap>

    <insert id="saveAll">
        INSERT INTO prime_cost(id,
                               factory,
                               product,
                               power_cost,
                               water_amount,
                               medical_amount,
                               medical_price,
                               record_time)VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (#{element.id},
             #{element.factory},
             #{element.product},
             #{element.powerCost},
             #{element.waterAmount},
             #{element.medicalAmount},
             #{element.medicalPrice},
             #{element.recordTime})
        </foreach>
        on conflict(factory, product, record_time) do update set power_cost     = excluded.power_cost,
                                                        water_amount   = excluded.water_amount,
                                                        medical_amount = excluded.medical_amount,
                                                        medical_price  = excluded.medical_price
    </insert>

    <select id="statistic" resultType="org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResult">
    </select>

    <insert id="adjustYMPrice">
        insert into factory_month_water_price(record_time, factory_name, price)
        VALUES (#{recordTime}, #{factory}, #{price})
    </insert>

    <select id="getWaterPriceInfoList"
            resultType="org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResultOfWaterPrice">
        select factory,
               price,
               coalesce(round(sum(case when cost.record_time between #{fromTime}::timestamp and #{toTime}::timestamp then water_amount end), 2), 0)         totalWaterAmount,
               coalesce(round(sum(case when cost.record_time between #{fromTime}::timestamp and #{toTime}::timestamp then water_amount end) * price, 2), 0) totalWaterPrice,
               round(sum(case when cost.record_time between #{fromTime}::timestamp - interval '1 year' and #{toTime}::timestamp - interval '1 year' then water_amount end) * price, 2) totalWaterPriceLastYear
        from prime_cost cost
                 left join factory_month_water_price monthly_price
                           on extract(year from cost.record_time) = extract(year from monthly_price.record_time)
                               and extract(month from cost.record_time) = extract(month from monthly_price.record_time)
                               and cost.factory = monthly_price.factory_name
        group by factory, price
    </select>

    <!--suppress SqlShadowingAlias -->
    <select id="getMedicalPriceInfoList"
            resultType="org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResultOfMedical">
        select c_factory                                                                        factory,
               c_product                                                                        product,
               c_medical_price                                                                  medicalPrice,
               coalesce(round(sum(case when cost.record_time between #{fromTime}::timestamp and #{toTime}::timestamp then medical_amount end), 2), 0)                                       totalMedicalAmount,
               coalesce(round(sum(case when cost.record_time between #{fromTime}::timestamp and #{toTime}::timestamp then medical_amount end) * c_medical_price, 2), 0)                     totalMedicalPrice,
               coalesce(round(sum(case when cost.record_time between #{fromTime}::timestamp and #{toTime}::timestamp then medical_amount end) * c_medical_price / (select sum(water_amount) from prime_cost where factory = c_factory and record_time between #{fromTime} and #{toTime}), 2), 0) pricePerWater,
               round(sum(case when cost.record_time between #{fromTime}::timestamp - interval '1 year' and #{toTime}::timestamp - interval '1 year' then medical_amount end) * c_medical_price, 2)totalMedicalPriceLastYear
        from
             <!--找出当前时间所有工厂-药品映射-->
             (select distinct factory c_factory from prime_cost where record_time between #{fromTime} and #{toTime}) a
                 cross join
             (select product c_product, medical_price c_medical_price from prime_cost where record_time between #{fromTime} and #{toTime}
                group by c_product, c_medical_price) b
                 left join prime_cost cost
                           on factory = c_factory
                               and product = c_product
                               and medical_price = c_medical_price
        group by c_factory, c_product, c_medical_price
        order by factory, product, medicalPrice
    </select>

    <!--suppress SqlShadowingAlias -->
    <select id="getPowerPriceInfoList" resultType="org.thingsboard.server.dimain.smartproduct.totalreport.primeCost.PrimeCostStatisticResultOfPower">
        select factory                                                        factory,
               coalesce(round(sum(case when cost.record_time between #{fromTime} and #{toTime} then power_cost end), 2), 0)                         totalPrice,
               coalesce(round(sum(case when cost.record_time between #{fromTime} and #{toTime} then power_cost end) / (select sum(water_amount) from prime_cost i where i.factory = cost.factory and record_time between #{fromTime} and #{toTime}), 2), 0) pricePerWater,
        round(sum(case when cost.record_time between #{fromTime}::timestamp - interval '1 year' and #{toTime}::timestamp - interval '1 year' then power_cost end), 2)    totalPriceLastYear
        from prime_cost cost
        group by factory
    </select>
</mapper>