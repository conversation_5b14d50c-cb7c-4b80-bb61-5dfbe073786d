-- 事件总览功能数据库表创建脚本

-- 创建事件总览表
CREATE TABLE IF NOT EXISTS event_overview (
    id VARCHAR(36) PRIMARY KEY,
    serial_no VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    source VARCHAR(50),
    type VARCHAR(50),
    level VARCHAR(20),
    address VARCHAR(500),
    remark TEXT,
    status VARCHAR(50) NOT NULL,
    organizer_id VARCHAR(36),
    process_user_id VARCHAR(36),
    step_process_user_id VARCHAR(36),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    tenant_id VARCHAR(36) NOT NULL,
    project_id VARCHAR(36),
    coordinate VARCHAR(50),
    coordinate_name VARCHAR(200),
    receive_department_id VARCHAR(36),
    cc_user_id VARCHAR(500),
    parent_id VARCHAR(36)
);

-- 添加表注释
COMMENT ON TABLE event_overview IS '事件总览表，用于管理和展示工单事件信息';

-- 添加字段注释
COMMENT ON COLUMN event_overview.id IS '主键ID';
COMMENT ON COLUMN event_overview.serial_no IS '工单编号，唯一标识';
COMMENT ON COLUMN event_overview.title IS '事件标题';
COMMENT ON COLUMN event_overview.source IS '事件来源';
COMMENT ON COLUMN event_overview.type IS '事件类型';
COMMENT ON COLUMN event_overview.level IS '紧急程度';
COMMENT ON COLUMN event_overview.address IS '发生地点';
COMMENT ON COLUMN event_overview.remark IS '事件描述';
COMMENT ON COLUMN event_overview.status IS '事件状态';
COMMENT ON COLUMN event_overview.organizer_id IS '创建人ID';
COMMENT ON COLUMN event_overview.process_user_id IS '处理人ID';
COMMENT ON COLUMN event_overview.step_process_user_id IS '当前步骤处理人ID';
COMMENT ON COLUMN event_overview.create_time IS '创建时间';
COMMENT ON COLUMN event_overview.update_time IS '更新时间';
COMMENT ON COLUMN event_overview.tenant_id IS '租户ID';
COMMENT ON COLUMN event_overview.project_id IS '项目ID';
COMMENT ON COLUMN event_overview.coordinate IS '地理位置坐标，格式：经度,纬度';
COMMENT ON COLUMN event_overview.coordinate_name IS '地理位置名称';
COMMENT ON COLUMN event_overview.receive_department_id IS '接收部门ID';
COMMENT ON COLUMN event_overview.cc_user_id IS '抄送人ID列表';
COMMENT ON COLUMN event_overview.parent_id IS '父工单ID';


