<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.device.SoDeviceMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        id,
        serial_id,
        type_serial_id,
        type_name,
        top_type_serial_id,
        top_type_name,
        name,
        model,
        mark,
        unit,
        order_num,
        remark,
        creator,
        create_time,
        so_device.tenant_id<!--@sql from so_device, so_device_type_map -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartOperation.device.SoDevice">
        <result column="id" property="id"/>
        <result column="serial_id" property="serialId"/>
        <result column="type_serial_id" property="typeSerialId"/>
        <result column="type_name" property="typeName"/>
        <result column="top_type_serial_id" property="topTypeSerialId"/>
        <result column="top_type_name" property="topTypeName"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="mark" property="mark"/>
        <result column="unit" property="unit"/>
        <result column="order_num" property="orderNum"/>
        <result column="remark" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_device
                 left join so_device_type_map type_map
                           on type_serial_id = current_type_serial_id
        <where>
            <if test="serialId != null and serialId != ''">
                and serial_id like '%' || #{serialId} || '%'
            </if>
            <if test="typeSerialId != null and typeSerialId != '' and typeSerialId != '00000000000000'">
                and type_map.type_path like '%,' || #{typeSerialId} || ',%'
            </if>
            <if test="name != null and name != ''">
                and name like '%' || #{name} || '%'
            </if>
            <if test="model != null and model != ''">
                and model like '%' || #{model} || '%'
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and so_device.tenant_id = #{tenantId}
        </where>
        order by order_num
    </select>

    <update id="update">
        update so_device
        <set>
            <if test="serialId != null and serialId != ''">
                serial_id = #{serialId},
            </if>
            <if test="typeSerialId != null and typeSerialId != ''">
                type_serial_id = #{typeSerialId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="model != null and model != ''">
                model = #{model},
            </if>
            <if test="mark != null">
                mark = #{mark},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_device
        set serial_id      = #{serialId},
            type_serial_id = #{typeSerialId},
            name           = #{name},
            model          = #{model},
            unit           = #{unit},
            mark           = #{mark},
            order_num      = #{orderNum},
            remark         = #{remark}
        where id = #{id}
    </update>

    <select id="selectCountByIdAndSerialId" resultType="int">
        select count(1)
        from so_device
        where id = #{id}
          and serial_id = #{serialId};
    </select>

    <select id="selectCountBySerialIdAndTenantId" resultType="int">
        select count(1)
        from so_device
        where serial_id = #{serialId}
          and tenant_id = #{tenantId};
    </select>
</mapper>