<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartOperation.construction.SoConstructionSettlementMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->
        settlement.id,
        construction.code                                                  as construction_code,
        construction.name                                                  as construction_name,
        construction.type_id                                               as construction_type_id,
        (select name from so_general_type where id = construction.type_id) as construction_type_name,
        settlement.process_user,
        <!--@formatter:off-->
        (select cost from so_construction_estimate
         where construction_code = construction.code and tenant_id = construction.tenant_id) as estimate_cost,
        (select sum(cost) from so_construction_contract
        where construction_code = settlement.construction_code and tenant_id = settlement.tenant_id) as contract_total_cost,
        (select sum(cost) from so_construction_expense
        where construction_code = settlement.construction_code and tenant_id = settlement.tenant_id) as expense_total_cost,
        <!--@formatter:on-->
        settlement.cost,
        settlement.address,
        info.status,
        settlement.remark,
        settlement.attachments,
        settlement.creator,
        settlement.create_time,
        settlement.update_user,
        settlement.update_time,
        construction.tenant_id<!--@sql from so_construction_settlement settlement, so_construction construction, so_construction_task_info info -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartOperation.construction.SoConstructionSettlement">
        <result column="id" property="id"/>
        <result column="construction_code" property="constructionCode"/>
        <result column="construction_name" property="constructionName"/>
        <result column="construction_type_id" property="constructionTypeId"/>
        <result column="construction_type_name" property="constructionTypeName"/>
        <result column="process_user" property="processUser"/>
        <result column="cost" property="cost"/>
        <result column="contract_total_cost" property="contractTotalCost"/>
        <result column="expense_total_cost" property="expenseTotalCost"/>
        <result column="address" property="address"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="attachments" property="attachments"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findByPage" resultMap="BaseResultMap">
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_SETTLEMENT"/>
        select
        <include refid="Base_Column_List"/>
        from so_construction construction
                 left join so_construction_settlement settlement
                           on settlement.construction_code = construction.code and
                              settlement.tenant_id = construction.tenant_id
                 left join so_construction_task_info info
                           on info.construction_code = construction.code and
                              info.tenant_id = construction.tenant_id
                               and info.scope = #{scope}
        <where>
            <if test="constructionCode != null and constructionCode != ''">
                and construction.code ilike '%' || #{constructionCode} || '%'
            </if>
            <if test="constructionName != null and constructionName != ''">
                and construction.name like '%' || #{constructionName} || '%'
            </if>
            <if test="constructionTypeId != null and constructionTypeId != ''">
                and construction.type_id = #{constructionTypeId}
            </if>
            <if test="fromTime != null">
                and settlement.create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and settlement.create_time &lt;= #{toTime}
            </if>
            and construction.tenant_id = #{tenantId}
        </where>
        order by construction.create_time desc
    </select>

    <update id="update">
        update so_construction_settlement
        <set>
            <!--            <if test="constructionCode != null">-->
            <!--                construction_code = #{constructionCode},-->
            <!--            </if>-->
            <if test="processUser != null">
                process_user = #{processUser},
            </if>
            <if test="cost != null">
                cost = #{cost},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="attachments != null">
                attachments = #{attachments},
            </if>
            update_user = #{updateUser},
            update_time = #{updateTime}
        </set>
        where id = #{id}
    </update>

    <update id="updateFully">
        update so_construction_settlement
        set process_user = #{processUser},
            cost         = #{cost},
            address      = #{address},
            remark       = #{remark},
            attachments  = #{attachments},
            update_user  = #{updateUser},
            update_time  = #{updateTime}
        where id = #{id}
    </update>

    <insert id="save">
        INSERT INTO so_construction_settlement(id,
                                               construction_code,
                                               process_user,
                                               cost,
                                               address,
                                               remark,
                                               attachments,
                                               creator,
                                               create_time,
                                               update_user,
                                               update_time,
                                               tenant_id)
        VALUES (#{id},
                #{constructionCode},
                #{processUser},
                #{cost},
                #{address},
                #{remark},
                #{attachments},
                #{creator},
                #{createTime},
                #{updateUser},
                #{updateTime},
                #{tenantId})
    </insert>

    <update id="markAsCompleted">
        <bind name="status"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.SoGeneralTaskStatus@COMPLETED"/>
        <bind name="scope"
              value="@org.thingsboard.server.dao.model.sql.smartOperation.project.SoGeneralSystemScope@SO_CONSTRUCTION_SETTLEMENT"/>
        update so_construction_task_info
        set status        = #{completeStatus},
            complete_time = now()
        where construction_code = (select code from so_construction_apply where id = #{id})
          and tenant_id = (select tenant_id from so_construction_apply where id = #{id})
          and scope = #{scope}
          and complete_time is null
    </update>

    <select id="getConstructionCodeById" resultType="java.lang.String">
        select construction_code
        from so_construction_settlement
        where id = #{id}
    </select>

    <select id="getIdByConstructionCodeAndTenantId" resultType="java.lang.String">
        select id
        from so_construction_settlement
        where construction_code = #{constructionCode}
          and tenant_id = #{tenantId}
    </select>
</mapper>