<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.DepartmentMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           parent_id,
                           "name",
                           "type",
                           order_num,
                           create_time,
                           tenant_id<!--@sql from tb_department-->
    </sql>
    <sql id="Parent_Column_Query">
        <!--@sql select -->id,
                           parent_id,
                           "name",
                           "type",
                           order_num,
                           create_time,
                           tenant_id,
                           department_get_name(child.parent_id) as parent_name,
                           2                                    as layer
    from tb_department child
    </sql>

    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.department.Department">
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="order_num" property="orderNum"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findChildren" resultType="org.thingsboard.server.dao.model.sql.department.Department">
        select
        <include refid="Parent_Column_Query"/>
        where parent_id = #{pid}
        <if test="tenantId != null and tenantId != ''">
            and tenant_id = #{tenantId}
        </if>
        order by order_num
    </select>

    <update id="update">
        update tb_department
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Parent_Column_Query"/>
        <where>
            <if test="parentId != null and parentId != ''">
                and department_at_department(parent_id, #{parentId})
            </if>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="deptIdList != null and deptIdList != ''">
                and id in
                <foreach collection="deptIdList.split(',')" item="element" open="(" close=")">
                    #{element}
                </foreach>
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <select id="getNameById" resultType="java.lang.String">
        select department_get_name(#{id})
    </select>

    <select id="getDepartmentId" resultType="java.lang.String">
        select department_id
        from tb_user
        where id = #{userId}
    </select>

    <select id="getDirectOrgByDepartmentId" resultType="java.lang.String">
        select department_get_direct_organization(#{id})
    </select>

    <delete id="removeAllDepartment">
        delete
        from tb_department
        where tenant_id = #{tenantId}
    </delete>

    <select id="getNameByMultiId" resultType="java.lang.String">
        select department_resolve_multi_id(#{multiId})
    </select>

    <select id="getInfoById" resultType="org.thingsboard.server.dao.util.imodel.response.DepartmentInfo">
        select id,
               name,
               department_get_direct_organization(id) directOrgId,
               1                                      flag
        from tb_department
        where id = #{departmentId}
    </select>

    <select id="canBeDelete" resultType="boolean">
        select
        <!--部门存在-->
        (select count(1) > 0 from tb_department where id = #{id})

        <!--没有下级-->
        <!--        and (select count(1) = 0 from tb_department where parent_id = #{id})-->

        <!--没有用户关联-->
        <!--        and (select count(1) = 0 from tb_user where department_id = #{id});-->
        and
        <include refid="subDepartmentHaveNotUserQuery"/>
    </select>

    <sql id="subDepartmentHaveNotUserQuery">
        (with recursive temp(id, amount) as
                            (select id,
                                    (select count(1)
                                     from tb_user
                                     where department_id = #{id})
                             from tb_department
                             where id = #{id}
                             union all
                             select dept.id, temp.amount + (select count(1) from tb_user where department_id = dept.id)
                             from tb_department dept,
                                  temp
                             where dept.parent_id = temp.id)
         select max(temp.amount) = 0
         from temp)
    </sql>

    <select id="canBeAdd" resultType="boolean">
        select
        <!--父节点存在-->
        (
                (select count(1) > 0 from tb_organization where id = #{parentId}) or
                (select count(1) > 0 from tb_department where id = #{parentId})
            )
        <!--父节点下没有用户-->
        and (select count(1) = 0 from tb_user where department_id = #{parentId})
    </select>

    <delete id="deleteWithChildrenRecursive">
        delete
        from tb_department
            using (with recursive temp(id) as (select #{id}::varchar
                                               union all
                                               select dept.id
                                               from tb_department dept,
                                                    temp
                                               where dept.parent_id = temp.id)
                   select temp.id
                   from temp) idList
        where tb_department.id = idList.id
    </delete>

    <select id="countUserByDepartment" resultType="java.lang.String">
        with result as (select department.name deptName, count(1) amount
                        from tb_department department
                                 left join tb_user u
                                           on u.department_id = department.id
                        where department.tenant_id = #{tenantId}
                          and u.tenant_id = #{tenantId}
                        group by department.name)
        select '{' || rtrim(string_agg('"' || deptName || '":' || amount, ','), ',') || '}'
        from result;
    </select>
</mapper>