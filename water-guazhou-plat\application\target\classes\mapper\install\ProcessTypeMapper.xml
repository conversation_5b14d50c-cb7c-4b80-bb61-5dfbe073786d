<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.install.ProcessTypeMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.install.ProcessType">
        select *
        from tb_install_process_type a
        where a.name like '%' || #{name} || '%'
        and a.tenant_id = #{tenantId}
        and a.type like '%' || #{type} || '%'
        order by a.code desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_install_process_type a
        where a.name like '%' || #{name} || '%' and a.tenant_id = #{tenantId}
    </select>

    <select id="getApplyData" resultType="org.thingsboard.server.dao.model.sql.Form">
        select c.*
        from tb_install_process_type a
        left join tb_install_process_step b on a.id = b.main_id
        left join tb_form c on b.form_id = c.id
        where a.id = #{typeId}
        order by b.order_num offset 0 limit 1
    </select>

</mapper>