<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.SmCircuitTaskCoordinateMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        task_code,
        x,
        y,
        creator,
        create_time,
        tenant_id
        <!--@sql from sm_circuit_task_coordinate -->
    </sql>
    <resultMap id="BaseResultMap"
               type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SmCircuitTaskCoordinate">
        <result column="id" property="id"/>
        <result column="task_code" property="taskCode"/>
        <result column="x" property="x"/>
        <result column="y" property="y"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sm_circuit_task_coordinate
        <where>
            <if test="taskCode != null and taskCode != ''">
                and task_code = #{taskCode}
            </if>
            <if test="fromTime != null">
                and create_time >= #{fromTime}
            </if>
            <if test="toTime != null">
                and create_time &lt;= #{toTime}
            </if>
            and tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <update id="updateFully">
        update sm_circuit_task_coordinate
        set creator   = #{creator},
            task_code = #{taskCode},
            x         = #{x},
            y         = #{y}
        where id = #{id}
    </update>
</mapper>