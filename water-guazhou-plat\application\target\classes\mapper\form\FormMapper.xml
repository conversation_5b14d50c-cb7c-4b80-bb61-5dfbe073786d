<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.form.FormMapper">

    <select id="getList" resultType="org.thingsboard.server.dao.model.sql.Form">
        select id, code, name, status, remark, tenant_id
        from tb_form a
        where a.name like '%' || #{name} || '%' and a.tenant_id = #{tenantId}
        order by a.code desc
        offset (#{page} - 1) * #{size} limit #{size}
    </select>

    <select id="getListCount" resultType="int">
        select count(*)
        from tb_form a
        where a.name like '%' || #{name} || '%' and a.tenant_id = #{tenantId}
    </select>

</mapper>