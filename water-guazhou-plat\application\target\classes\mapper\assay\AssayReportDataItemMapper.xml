<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.assay.AssayReportDataItemMapper">

    <insert id="batchInsert">
        INSERT INTO tb_assay_report_data_item(pid, title, target, check_person, tenant_id, create_time, id, unit) values
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.pid},
            #{element.title},
            #{element.target},
            #{element.checkPerson},
            #{element.tenantId},
            #{element.createTime},
            #{element.id},
            #{element.unit}
            )
        </foreach>
    </insert>

    <select id="getListByPid" resultType="org.thingsboard.server.dao.model.sql.assay.AssayReportDataItem">
        select * from tb_assay_report_data_item where pid = #{pid}
    </select>

</mapper>