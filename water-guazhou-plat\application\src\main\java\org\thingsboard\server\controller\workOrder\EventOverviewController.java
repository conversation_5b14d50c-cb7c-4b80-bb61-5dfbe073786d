package org.thingsboard.server.controller.workOrder;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.BaseController;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.orderWork.EventOverviewService;
import org.thingsboard.server.dao.util.imodel.IModel;
import org.thingsboard.server.dao.util.imodel.query.workOrder.EventOverviewPageRequest;
import org.thingsboard.server.dao.util.imodel.response.EventOverviewStatisticsResponse;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.annotation.IStarController;

/**
 * 事件总览控制器
 * 
 * <AUTHOR>
 */
@RequestMapping("api/eventOverview")
@IStarController
public class EventOverviewController extends BaseController {

    @Autowired
    private EventOverviewService eventOverviewService;

    /**
     * 分页查询事件总览
     * 
     * @param request 查询参数
     * @return 分页结果
     */
    @GetMapping
    public IPage<WorkOrder> getEventOverviewPage(EventOverviewPageRequest request) throws ThingsboardException {
        request.setTenantId(getTenantId().getId().toString());
        return eventOverviewService.findEventOverviewByPage(request);
    }

    /**
     * 获取事件详情
     * 
     * @param id 事件ID
     * @return 事件详情
     */
    @GetMapping("/{id}")
    public WorkOrder getEventDetail(@PathVariable String id) throws ThingsboardException {
        return eventOverviewService.findEventById(id, getTenantId().getId().toString());
    }

    /**
     * 更新事件坐标信息
     * 
     * @param id 事件ID
     * @param model 请求模型
     * @return 更新结果
     */
    @PutMapping("/{id}/coordinate")
    public IstarResponse updateEventCoordinate(@PathVariable String id, @RequestBody IModel model) throws ThingsboardException {
        String coordinate = model.get("coordinate", String.class);
        eventOverviewService.updateEventCoordinate(id, coordinate, getTenantId().getId().toString());
        return IstarResponse.ok();
    }

    /**
     * 获取事件统计信息
     * 
     * @param fromTime 开始时间
     * @param toTime 结束时间
     * @param source 来源
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public IstarResponse getEventStatistics(
            @RequestParam(required = false) Long fromTime,
            @RequestParam(required = false) Long toTime,
            @RequestParam(required = false) String source) throws ThingsboardException {
        
        EventOverviewStatisticsResponse statistics = eventOverviewService.getEventStatistics(
                fromTime, toTime, source, getTenantId().getId().toString());
        return IstarResponse.ok(statistics);
    }

    /**
     * 批量更新事件坐标
     * 
     * @param model 批量更新参数
     * @return 更新结果
     */
    @PutMapping("/coordinates/batch")
    public IstarResponse batchUpdateCoordinates(@RequestBody IModel model) throws ThingsboardException {
        // 批量更新逻辑，可以根据地址自动获取坐标
        eventOverviewService.batchUpdateCoordinates(model, getTenantId().getId().toString());
        return IstarResponse.ok();
    }

    /**
     * 根据地址获取坐标
     * 
     * @param address 地址
     * @return 坐标信息
     */
    @GetMapping("/geocoding")
    public IstarResponse getCoordinateByAddress(@RequestParam String address) {
        String coordinate = eventOverviewService.getCoordinateByAddress(address);
        return IstarResponse.ok(coordinate);
    }
}
