<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.department.OrganizationMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           "name",
                           "type",
                           phone,
                           parent_id,
                           order_num,
                           create_time,
                           tenant_id,
                           location,
                           style_information,
                           org_type<!--@sql from tb_organization-->
    </sql>
    <sql id="Parent_Column_Query">
        <!--@sql select -->id,
                           "name",
                           "type",
                           phone,
                           parent_id,
                           order_num,
                           create_time,
                           tenant_id,
                           location,
                           style_information,
                           org_type,
                           (select name from tb_organization where id = child.parent_id) as parent_name,
                           1                                                             as layer
    from tb_organization child
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.department.Organization">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="phone" property="phone"/>
        <result column="parent_id" property="parentId"/>
        <result column="order_num" property="orderNum"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="location" property="location"/>
        <result column="style_information" property="styleInformation"/>
        <result column="org_type" property="orgType"/>
    </resultMap>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Parent_Column_Query"/>
        <where>
            <if test="name != null and name != ''">
                and "name" like '%' || #{name} || '%'
            </if>
            <if test="parentId != null and parentId != ''">
                and parent_id = #{parentId}
            </if>
            and tenant_id = #{tenantId}
        </where>
    </select>

    <update id="update">
        update tb_organization
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="location != null">
                location = #{location},
            </if>
            <if test="styleInformation != null">
                style_information = #{styleInformation},
            </if>
            <if test="orgType != null">
                org_type = #{orgType},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="findChildren" resultMap="BaseResultMap">
        select
        <include refid="Parent_Column_Query"/>
        where parent_id = #{pid}
          and tenant_id = #{tenantId}
        order by order_num
    </select>
    <select id="findChildrenIdString" resultType="java.lang.String">
        select id
        from tb_organization
        where parent_id = #{orgId}
        order by order_num
    </select>

    <delete id="removeAllOrganization">
        delete
        from tb_organization
        where tenant_id = #{tenantId}
    </delete>

    <select id="getInfoById" resultType="org.thingsboard.server.dao.util.imodel.response.DepartmentInfo">
        select id,
               name,
               null directOrgId,
               2    flag
        from tb_organization
        where id = #{departmentId}
    </select>

    <select id="canBeAdd" resultType="boolean">
        select
        <!--父级单位需要存在-->
        (select count(1) > 0 from tb_organization where id = #{parentId})
        <!--其下没有部门-->
        and (select count(1) = 0 from tb_department where parent_id = #{parentId})
    </select>

    <select id="canBeDelete" resultType="boolean">
        select
        <!--当前单位需要存在-->
        (select count(1) > 0 from tb_organization where id = #{id})
        <!--没有子级单位-->
        and (select count(1) = 0 from tb_organization where parent_id = #{id})
        <!--没有子级部门-->
        and (select count(1) = 0 from tb_department where parent_id = #{id})
        <!--其下没有用户关联-->
        <!--        and (<include refid="subOrganizationAndDepartmentHaveNotUserQuery"/>)-->
        and (select count(1) = 0 from tb_user where department_id = #{id})
    </select>

    <sql id="subOrganizationAndDepartmentHaveNotUserQuery">
        with recursive
            temp_org(id, amount) as (select id,
                                            (select count(1)
                                             from tb_user
                                             where department_id = #{id})
                                     from tb_organization
                                     where id = #{id}
                                     union all
                                     select org.id,
                                            temp.amount + (select count(1) from tb_user where department_id = org.id)
                                     from tb_organization org,
                                          temp_org temp
                                     where org.parent_id = temp.id),
            temp_dept(id, amount) as (select id,
                                             (select amount from temp_org where id = tb_department.parent_id) +
                                             (select count(1) from tb_user where department_id = tb_department.id)
                                      from tb_department
                                      where parent_id in (select id from temp_org)
                                      union all
                                      select dept.id,
                                             temp.amount +
                                             (select count(1) from tb_user where department_id = dept.id)
                                      from tb_department dept,
                                           temp_dept temp
                                      where dept.parent_id = temp.id)
        select max(temp_dept.amount) = 0
        from temp_dept
    </sql>
</mapper>