<!-- 事件总览 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
    <MapLocationDialog
      v-model="mapDialogVisible"
      :address="selectedAddress"
      :coordinates="selectedCoordinates"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { Refresh, Plus } from '@element-plus/icons-vue'
import { SLMessage } from '@/utils/Message'
import { GetEventOverviewPage } from '@/api/workorder/eventOverview'
import request from '@/plugins/axios'
import {
  WorkOrderStatusRecord
} from './config'
import { formatterDate } from '@/utils/GlobalHelper'
import MapLocationDialog from './components/MapLocationDialog.vue'
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()

// 地图弹窗相关
const mapDialogVisible = ref(false)
const selectedAddress = ref('')
const selectedCoordinates = ref('')

// 查询配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'date',
      label: '创建时间',
      type: 'daterange'
    },
    {
      field: 'title',
      label: '名称',
      type: 'input',
      placeholder: '请输入事件名称'
    },
    {
      field: 'type',
      label: '类型',
      type: 'select',
      options: []
    },
    {
      field: 'status',
      label: '状态',
      type: 'select',
      options: []
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'success',
          text: '新建',
          svgIcon: shallowRef(Plus),
          click: () => handleCreate()
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    date: [
      moment().subtract(7, 'day').format(formatterDate),
      moment().format(formatterDate)
    ]
  }
})

// 表格配置
const TableConfig = reactive<ICardTable>({
  loading: false,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  columns: [
    {
      minWidth: 180,
      prop: 'workOrderId',
      label: '工单编号'
    },
    {
      minWidth: 150,
      prop: 'title',
      label: '事件标题',
      showOverflowTooltip: true
    },
    {
      minWidth: 120,
      prop: 'type',
      label: '类型'
    },
    {
      minWidth: 100,
      prop: 'level',
      label: '紧急程度'
    },
    {
      minWidth: 150,
      prop: 'address',
      label: '发生地点',
      showOverflowTooltip: true,
      cellStyle: (row: any) => {
        // 如果有坐标信息，显示为可点击的链接样式
        if (row.coordinate) {
          return {
            color: '#409eff',
            cursor: 'pointer',
            textDecoration: 'underline'
          }
        }
        return {}
      },
      handleClick: (row: any) => {
        // 如果有坐标信息，打开地图
        if (row.coordinate) {
          handleAddressClick(row.address, row.coordinate)
        }
      }
    },
    {
      minWidth: 120,
      prop: 'status',
      label: '状态',
      tag: true,
      tagColor: (row: any): string => {
        const statusColorMap: Record<string, string> = {
          'PENDING': '#e6a23c',
          'ASSIGN': '#409eff',
          'RESOLVING': '#67c23a',
          'ARRIVING': '#67c23a',
          'PROCESSING': '#67c23a',
          'SUBMIT': '#909399',
          'REVIEW': '#909399',
          'CHARGEBACK_REVIEW': '#909399',
          'HANDOVER_REVIEW': '#909399',
          'APPROVED': '#67c23a',
          'COMPLETE': '#67c23a',
          'REJECTED': '#f56c6c',
          'TERMINATED': '#f56c6c',
          'CHARGEBACK': '#f56c6c'
        }
        return statusColorMap[row.status] || '#909399'
      },
      formatter: (row: any) => {
        return WorkOrderStatusRecord[row.status as keyof typeof WorkOrderStatusRecord] || row.statusName || '-'
      }
    },
    {
      minWidth: 100,
      prop: 'organizerIdName',
      label: '创建人'
    },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '创建时间',
      formatter: (row: any) => {
        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      }
    }
  ],
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      click: (row: any) => handleView(row)
    }
  ]
})

// 刷新数据
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2
      ? [
          moment(query.date[0], formatterDate).valueOf(),
          moment(query.date[1], formatterDate).endOf('D').valueOf()
        ]
      : [
          moment().subtract(7, 'day').startOf('D').valueOf(),
          moment().endOf('D').valueOf()
        ]

    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date

    const res = await GetEventOverviewPage(params)
    const data = res.data?.data
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  TableConfig.loading = false
}

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshData()
}

// 处理地址点击
const handleAddressClick = (address: string, coordinate?: string) => {
  selectedAddress.value = address
  selectedCoordinates.value = coordinate || ''
  mapDialogVisible.value = true
}

// 新建事件
const handleCreate = () => {
  // 跳转到新建工单页面
  window.open('/workorder/NewOrder', '_blank')
}

// 查看详情
const handleView = (row: any) => {
  // 跳转到工单详情页面
  window.open(`/workorder/OrderDetail?id=${row.id}`, '_blank')
}



// 初始化选项
const initOptions = async () => {
  // 初始化状态选项 - 只保留指定的5个状态
  const statusField = SearchConfig.filters?.find(f => f.field === 'status') as any
  if (statusField) {
    statusField.options = [
      { label: '待审核', value: 'PENDING' },
      { label: '已分派', value: 'ASSIGN' },
      { label: '处理中', value: 'PROCESSING' },
      { label: '已完成', value: 'COMPLETE' },
      { label: '已撤回', value: 'TERMINATED' }
    ]
  }

  // 初始化类型选项 - 从API获取父级类型
  const typeField = SearchConfig.filters?.find(f => f.field === 'type') as any
  if (typeField) {
    try {
      const res = await request({
        url: '/api/workOrderType/list',
        method: 'get',
        params: { status: 1 }
      })

      if (res.data?.data) {
        // 只获取父级类型（没有parentId或parentId为空的）
        const parentTypes = res.data.data.filter((item: any) => !item.parentId)
        typeField.options = parentTypes.map((item: any) => ({
          label: item.name,
          value: item.id
        }))
      }
    } catch (error) {
      console.error('获取工单类型失败:', error)
      SLMessage.error('获取工单类型失败')
    }
  }
}

onMounted(async () => {
  await initOptions()
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .card-table {
    flex: 1;
    margin-top: 16px;
  }

  .system-notice {
    margin-top: 8px;
    padding: 8px 16px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .notice-text {
      font-size: 12px;
      color: #f56c6c;
    }
  }
}

:deep(.address-link) {
  &:hover {
    color: #66b1ff !important;
  }
}
</style>
