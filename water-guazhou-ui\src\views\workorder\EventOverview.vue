<!-- 事件总览 -->
<template>
  <div class="wrapper">
    <CardSearch ref="refSearch" :config="SearchConfig"></CardSearch>
    <CardTable ref="refTable" class="card-table" :config="TableConfig" />
    <MapLocationDialog
      v-model="mapDialogVisible"
      :address="selectedAddress"
      :coordinates="selectedCoordinates"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { Refresh, Plus } from '@element-plus/icons-vue'
import { SLMessage } from '@/utils/Message'
import { GetWorkOrderPage } from '@/api/workorder'
import {
  getEmergencyLevelOpetions,
  getFromOptions,
  getOrderTypeOptions,
  formatWorkOrderStatus,
  WorkOrderStatusRecord
} from './config'
import { formatterDate } from '@/utils/GlobalHelper'
import MapLocationDialog from './components/MapLocationDialog.vue'
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()

// 地图弹窗相关
const mapDialogVisible = ref(false)
const selectedAddress = ref('')
const selectedCoordinates = ref('')

// 查询配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'date',
      label: '创建时间',
      type: 'daterange'
    },
    {
      field: 'source',
      label: '来源',
      type: 'select',
      options: getFromOptions()
    },
    {
      field: 'organizerId',
      label: '创建人',
      type: 'select',
      options: []
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        },
        {
          perm: true,
          type: 'success',
          text: '新建',
          svgIcon: shallowRef(Plus),
          click: () => handleCreate()
        }
      ]
    }
  ]
})

// 表格配置
const TableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0
  },
  columns: [
    {
      label: '事件名称',
      prop: 'title',
      width: 200,
      showOverflowTooltip: true
    },
    {
      label: '上报地点',
      prop: 'uploadAddress',
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: '发生地点',
      prop: 'address',
      width: 180,
      showOverflowTooltip: true
    },
    {
      label: '事件状态',
      prop: 'statusName',
      width: 120,
      tag: true,
      tagColor: (row: any): string => {
        const statusColorMap: Record<string, string> = {
          'PENDING': '#e6a23c',
          'ASSIGN': '#409eff',
          'RESOLVING': '#67c23a',
          'ARRIVING': '#67c23a',
          'PROCESSING': '#67c23a',
          'SUBMIT': '#909399',
          'REVIEW': '#909399',
          'CHARGEBACK_REVIEW': '#909399',
          'HANDOVER_REVIEW': '#909399',
          'APPROVED': '#67c23a',
          'COMPLETE': '#67c23a',
          'REJECTED': '#f56c6c',
          'TERMINATED': '#f56c6c',
          'CHARGEBACK': '#f56c6c'
        }
        return statusColorMap[row.status] || '#909399'
      },
      formatter: (row: any) => {
        return WorkOrderStatusRecord[row.status as keyof typeof WorkOrderStatusRecord] || row.statusName || '-'
      }
    },
    {
      label: '创建时间',
      prop: 'createTime',
      width: 160,
      formatter: (row: any) => {
        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      }
    },
    {
      label: '创建人',
      prop: 'organizerIdName',
      width: 120
    },

  ],
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      click: (row: any) => handleView(row)
    },
    {
      perm: true,
      text: '地图定位',
      isTextBtn: true,
      disabled: (row: any) => !row.address,
      click: (row: any) => handleAddressClick(row.address, row.coordinate)
    }
  ]
})

// 刷新数据
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2
      ? [
          moment(query.date[0], formatterDate).valueOf(),
          moment(query.date[1], formatterDate).endOf('D').valueOf()
        ]
      : [
          moment().subtract(7, 'day').startOf('D').valueOf(),
          moment().endOf('D').valueOf()
        ]

    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date

    const res = await GetWorkOrderPage(params)
    const data = res.data?.data
    TableConfig.dataList = data?.data || []
    TableConfig.pagination.total = data?.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  TableConfig.loading = false
}

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  TableConfig.pagination.page = 1
  refreshData()
}

// 处理地址点击
const handleAddressClick = (address: string, coordinate?: string) => {
  selectedAddress.value = address
  selectedCoordinates.value = coordinate || ''
  mapDialogVisible.value = true
}

// 新建事件
const handleCreate = () => {
  // 跳转到新建工单页面
  window.open('/workorder/NewOrder', '_blank')
}

// 查看详情
const handleView = (row: any) => {
  // 跳转到工单详情页面
  window.open(`/workorder/OrderDetail?id=${row.id}`, '_blank')
}



onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .card-table {
    flex: 1;
    margin-top: 16px;
  }

  .system-notice {
    margin-top: 8px;
    padding: 8px 16px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .notice-text {
      font-size: 12px;
      color: #f56c6c;
    }
  }
}

:deep(.address-link) {
  &:hover {
    color: #66b1ff !important;
  }
}
</style>
