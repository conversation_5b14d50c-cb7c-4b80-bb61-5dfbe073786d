<!-- 事件总览 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="事件总览"
    :detail-max-min="true"
    :hide-right-drawer="true"
    :hide-detail-close="true"
    :map-config="mapConfig"
    @map-loaded="onMapLoaded"
  >
    <template #detail-header>
      <span>事件总览</span>
    </template>
    <template #detail-default>
      <div class="detail-wrapper">
        <div class="search-and-create">
          <CardSearch ref="refSearch" :config="SearchConfig" style="flex: 1; margin-right: 8px;"></CardSearch>
          <CreateEventButton @created="refreshData" />
        </div>
        <div class="detail-table">
          <CardTable ref="refTable" :config="TableConfig"></CardTable>
        </div>
      </div>
    </template>
  </RightDrawerMap>

  <!-- 事件表单弹窗 -->
  <EventOverviewForm
    v-model="showEventForm"
    :is-edit="isEditMode"
    :edit-data="editEventData"
    @submit="handleEventSubmit"
  />
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, shallowRef } from 'vue'
import { Refresh, Plus, Edit } from '@element-plus/icons-vue'
import { SLMessage } from '@/utils/Message'
import { GetEventOverviewPage } from '@/api/workorder/eventOverview'
import request from '@/plugins/axios'
import {
  WorkOrderStatusRecord
} from './config'
import { formatterDate } from '@/utils/GlobalHelper'
import RightDrawerMap from '@/views/arcMap/components/common/RightDrawerMap.vue'
import EventOverviewForm from './components/EventOverviewForm.vue'
import CreateEventButton from './components/CreateEventButton.vue'
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol'
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol'
import PopupTemplate from '@arcgis/core/PopupTemplate'
const refSearch = ref<ICardSearchIns>()
const refTable = ref<ICardTableIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()

// 表单相关
const showEventForm = ref(false)
const isEditMode = ref(false)
const editEventData = ref<any>(null)

// 地图相关
let mapView: __esri.MapView | null = null

// 地图配置
const mapConfig = reactive<IFormGisConfig>({
  defaultBaseMap: 'img_w',
  defaultCenter: [98.4842, 40.1677], // 瓜州县中心坐标
  zoom: 10
})

// 查询配置
const SearchConfig = reactive<ISearch>({
  filters: [
    {
      field: 'date',
      label: '创建时间',
      type: 'daterange'
    },
    {
      field: 'title',
      label: '名称',
      type: 'input',
      placeholder: '请输入事件名称'
    },
    {
      field: 'type',
      label: '类型',
      type: 'select',
      options: []
    },
    {
      field: 'status',
      label: '状态',
      type: 'select',
      options: []
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          type: 'primary',
          text: '查询',
          icon: 'iconfont icon-chaxun',
          click: () => refreshData()
        },
        {
          perm: true,
          type: 'default',
          text: '重置',
          svgIcon: shallowRef(Refresh),
          click: () => resetForm()
        }
      ]
    }
  ],
  handleSearch: () => refreshData(),
  defaultParams: {
    date: [
      moment().subtract(7, 'day').format(formatterDate),
      moment().format(formatterDate)
    ]
  }
})

// 表格配置
const TableConfig = reactive<ICardTable>({
  loading: false,
  dataList: [],
  pagination: {
    refreshData: ({ page, size }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  columns: [
    {
      minWidth: 180,
      prop: 'workOrderId',
      label: '工单编号'
    },
    {
      minWidth: 150,
      prop: 'title',
      label: '事件标题',
      showOverflowTooltip: true
    },
    {
      minWidth: 120,
      prop: 'type',
      label: '类型'
    },
    {
      minWidth: 150,
      prop: 'address',
      label: '发生地点',
      showOverflowTooltip: true,
      cellStyle: (row: any) => {
        // 所有地址都显示为可点击的链接样式（用于测试）
        return {
          color: '#409eff',
          cursor: 'pointer',
          textDecoration: 'underline'
        }
      },
      handleClick: (row: any) => {
        console.log('点击地址列，行数据:', row)

        // 首先进行地图定位
        if (row.coordinate) {
          handleAddressClick(row.address, row.coordinate)
        } else {
          // 如果没有坐标，使用测试坐标
          console.log('没有坐标信息，使用测试坐标')
          handleAddressClick(row.address || '测试地址', '98.4842,40.1677')
        }

        // 定位完成后，将地图设置为中等大小显示（占屏幕50%）
        setTimeout(() => {
          console.log('地图定位完成，将地图设置为中等大小显示')
          refMap.value?.toggleCustomDetailMaxmin('normal')
        }, 1000)
      }
    },
    {
      minWidth: 120,
      prop: 'status',
      label: '状态',
      tag: true,
      tagColor: (row: any): string => {
        const statusColorMap: Record<string, string> = {
          'PENDING': '#e6a23c',
          'ASSIGN': '#409eff',
          'RESOLVING': '#67c23a',
          'ARRIVING': '#67c23a',
          'PROCESSING': '#67c23a',
          'SUBMIT': '#909399',
          'REVIEW': '#909399',
          'CHARGEBACK_REVIEW': '#909399',
          'HANDOVER_REVIEW': '#909399',
          'APPROVED': '#67c23a',
          'COMPLETE': '#67c23a',
          'REJECTED': '#f56c6c',
          'TERMINATED': '#f56c6c',
          'CHARGEBACK': '#f56c6c'
        }
        return statusColorMap[row.status] || '#909399'
      },
      formatter: (row: any) => {
        return WorkOrderStatusRecord[row.status as keyof typeof WorkOrderStatusRecord] || row.statusName || '-'
      }
    },
    {
      minWidth: 100,
      prop: 'organizerName',
      label: '创建人'
    },
    {
      minWidth: 160,
      prop: 'createTime',
      label: '创建时间',
      formatter: (row: any) => {
        return row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
      }
    }
  ],
  operations: [
    {
      perm: true,
      text: '查看',
      type: 'success',
      isTextBtn: true,
      click: (row: any) => handleView(row)
    },
    {
      perm: true,
      text: '编辑',
      type: 'primary',
      isTextBtn: true,
      click: (row: any) => handleEdit(row)
    }
  ]
})

// 刷新数据
const refreshData = async () => {
  TableConfig.loading = true
  try {
    const query = refSearch.value?.queryParams || {}
    const [fromTime, toTime] = query.date?.length === 2
      ? [
          moment(query.date[0], formatterDate).valueOf(),
          moment(query.date[1], formatterDate).endOf('D').valueOf()
        ]
      : [
          moment().subtract(7, 'day').startOf('D').valueOf(),
          moment().endOf('D').valueOf()
        ]

    const params: any = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      ...query,
      fromTime,
      toTime
    }
    delete params.date

    const res = await GetEventOverviewPage(params)
    const data = res.data?.data
    TableConfig.dataList = data.data || []
    TableConfig.pagination.total = data.total || 0
  } catch (error) {
    SLMessage.error('查询失败')
  }
  TableConfig.loading = false
}

// 重置表单
const resetForm = () => {
  refSearch.value?.resetForm()
  refreshData()
}

// 处理地址点击 - 在地图上显示事件位置
const handleAddressClick = (address: string, coordinate?: string) => {
  console.log('点击地址:', address, '坐标:', coordinate)
  console.log('mapView状态:', mapView)

  if (!coordinate) {
    SLMessage.warning('该事件暂无位置信息')
    return
  }

  if (!mapView) {
    SLMessage.warning('地图尚未加载完成，请稍后重试')
    return
  }

  try {
    const coords = coordinate.split(',')
    if (coords.length !== 2) {
      SLMessage.error('坐标格式错误')
      return
    }

    const longitude = parseFloat(coords[0])
    const latitude = parseFloat(coords[1])

    if (isNaN(longitude) || isNaN(latitude)) {
      SLMessage.error('坐标数据无效')
      return
    }

    // 清除之前的标记
    mapView.graphics.removeAll()

    // 创建标记点 - 参考lookBoard的实现
    const point = new Point({
      longitude: longitude,
      latitude: latitude,
      spatialReference: mapView.spatialReference
    })

    const markerSymbol = new SimpleMarkerSymbol({
      color: [226, 119, 40],
      outline: {
        color: [255, 255, 255],
        width: 2
      },
      size: 14
    })

    const popupTemplate = new PopupTemplate({
      title: '事件位置',
      content: `
        <div style="padding: 8px;">
          <p><strong>地址：</strong>${address}</p>
          <p><strong>经度：</strong>${longitude}</p>
          <p><strong>纬度：</strong>${latitude}</p>
        </div>
      `
    })

    const graphic = new Graphic({
      geometry: point,
      symbol: markerSymbol,
      popupTemplate: popupTemplate,
      attributes: {
        address: address,
        longitude: longitude,
        latitude: latitude
      }
    })

    console.log('创建的graphic:', graphic)
    console.log('mapView.graphics:', mapView.graphics)

    mapView.graphics.add(graphic)

    console.log('添加graphic后，mapView.graphics.length:', mapView.graphics.length)

    // 定位到该点 - 使用goTo方法
    mapView.goTo({
      target: point,
      zoom: 16
    }).then(() => {
      console.log('地图定位完成')
    }).catch((error) => {
      console.error('地图定位失败:', error)
    })

    SLMessage.success('已定位到事件位置')
  } catch (error) {
    console.error('地图定位失败:', error)
    SLMessage.error('地图定位失败')
  }
}

// 编辑事件
const handleEdit = (row: any) => {
  isEditMode.value = true
  editEventData.value = { ...row }
  showEventForm.value = true
}

// 查看详情
const handleView = (row: any) => {
  // 跳转到工单详情页面
  window.open(`/workorder/OrderDetail?id=${row.id}`, '_blank')
}

// 处理事件表单提交（编辑）
const handleEventSubmit = async (data: any) => {
  try {
    // 编辑事件
    await request({
      url: `/api/workorder/event/${data.id}`,
      method: 'put',
      data
    })
    SLMessage.success('事件更新成功')

    showEventForm.value = false
    refreshData()
  } catch (error) {
    console.error('事件更新失败:', error)
    SLMessage.error('事件更新失败')
  }
}



// 初始化选项
const initOptions = async () => {
  // 初始化状态选项 - 只保留指定的5个状态
  const statusField = SearchConfig.filters?.find(f => f.field === 'status') as any
  if (statusField) {
    statusField.options = [
      { label: '待审核', value: 'PENDING' },
      { label: '已分派', value: 'ASSIGN' },
      { label: '处理中', value: 'PROCESSING' },
      { label: '已完成', value: 'COMPLETE' },
      { label: '已撤回', value: 'TERMINATED' }
    ]
  }

  // 初始化类型选项 - 从API获取父级类型
  const typeField = SearchConfig.filters?.find(f => f.field === 'type') as any
  if (typeField) {
    try {
      const res = await request({
        url: '/api/workOrderType/list',
        method: 'get',
        params: { status: 1 }
      })

      if (res.data?.data) {
        // 只获取父级类型（没有parentId或parentId为空的）
        const parentTypes = res.data.data.filter((item: any) => !item.parentId)
        typeField.options = parentTypes.map((item: any) => ({
          label: item.name,
          value: item.id
        }))
      }
    } catch (error) {
      console.error('获取工单类型失败:', error)
      SLMessage.error('获取工单类型失败')
    }
  }
}

// 地图加载完成回调
const onMapLoaded = (view: __esri.MapView) => {
  mapView = view
  console.log('地图加载完成, view:', view)
  console.log('mapView已设置:', mapView)

  // 延迟一下确保组件完全加载
  setTimeout(() => {
    // 默认打开详情面板并设置为最大化状态（列表优先显示，参考TaskDispatch）
    console.log('正在打开详情面板并设置为最大化状态...')
    refMap.value?.toggleCustomDetail(true)
    refMap.value?.toggleCustomDetailMaxmin('max')
    console.log('详情面板状态:', refMap.value?.isCustomOpened())
  }, 100)
}

onMounted(async () => {
  await initOptions()
  refreshData()
})
</script>

<style lang="scss" scoped>
.detail-wrapper {
  height: 100%;

  .search-and-create {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .detail-table {
    height: calc(100% - 60px);
  }
}

:deep(.address-link) {
  color: #409eff;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #66b1ff;
  }
}
</style>
