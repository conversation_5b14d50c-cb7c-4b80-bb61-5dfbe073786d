<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="org.thingsboard.server.dao.sql.report.ReportQueryMapper">

    <insert id="batchInsert">
        insert into tb_report_query(id, pid, database_id, content, create_time, tenant_id, report_id) VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id},
            #{element.pid},
            #{element.databaseId},
            #{element.content},
            #{element.createTime},
            #{element.tenantId},
            #{element.reportId}
            )
        </foreach>
    </insert>
</mapper>